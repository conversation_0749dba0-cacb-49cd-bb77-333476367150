{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Nha8NBSKK9bnapboUlhbuxGHPbKnPIOeYq/6s+W69NE=", "__NEXT_PREVIEW_MODE_ID": "08636b4ecae3f1da06aaa4363c4b601b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e7fe51a70fbe75e88fcbdf43be66a47983620ae9e87d1dbcf1edc5f2fa8f3ae6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "37f36ef4f0ab95d6c43f18cf5bb9debadfa15da32c8a567f299837bc30cc2f36"}}}, "sortedMiddleware": ["/"], "functions": {}}