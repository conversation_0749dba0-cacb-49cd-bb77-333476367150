"use client";

const DID_API_URL = "https://api.d-id.com";

export interface Agent {
  id: string;
  preview_name: string;
  status: string;
  presenter: {
    type: string;
    voice: {
      type: string;
      voice_id: string;
    };
    thumbnail: string;
    source_url: string;
  };
  llm: {
    type: string;
    provider: string;
    model: string;
    instructions: string;
  };
}

export interface StreamSession {
  id: string;
  offer: RTCSessionDescriptionInit;
  ice_servers: RTCIceServer[];
  session_id: string;
}

export interface ChatSession {
  id: string;
  agent_id: string;
}

export class DIDLiveStreamingService {
  private apiKey: string;
  private peerConnection: RTCPeerConnection | null = null;
  private streamId: string | null = null;
  private sessionId: string | null = null;
  private agentId: string | null = null;
  private chatId: string | null = null;
  private statsIntervalId: NodeJS.Timeout | null = null;
  private videoIsPlaying: boolean = false;
  private lastBytesReceived: number = 0;

  // Event callbacks
  public onVideoStatusChange?: (isPlaying: boolean, stream?: MediaStream) => void;
  public onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
  public onIceConnectionStateChange?: (state: RTCIceConnectionState) => void;
  public onAgentMessage?: (message: string) => void;
  public onError?: (error: string) => void;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  private getAuthHeaders() {
    return {
      "Authorization": `Basic ${this.apiKey}`,
      "Content-Type": "application/json",
    };
  }

  private async fetchWithRetries(url: string, options: RequestInit, retries = 1): Promise<Response> {
    const maxRetryCount = 3;
    const maxDelaySec = 4;
    
    try {
      return await fetch(url, options);
    } catch (err) {
      if (retries <= maxRetryCount) {
        const delay = Math.min(Math.pow(2, retries) / 4 + Math.random(), maxDelaySec) * 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));
        console.log(`Request failed, retrying ${retries}/${maxRetryCount}. Error ${err}`);
        return this.fetchWithRetries(url, options, retries + 1);
      } else {
        throw new Error(`Max retries exceeded. error: ${err}`);
      }
    }
  }

  async createAgent(instructions: string, agentName: string): Promise<Agent> {
    const payload = {
      presenter: {
        type: "talk",
        voice: {
          type: "microsoft",
          voice_id: "en-US-JennyMultilingualV2Neural"
        },
        thumbnail: "https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg",
        source_url: "https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg"
      },
      llm: {
        type: "openai",
        provider: "openai",
        model: "gpt-4o-mini",
        instructions: instructions
      },
      preview_name: agentName
    };

    try {
      const response = await this.fetchWithRetries(`${DID_API_URL}/agents`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const agentData: Agent = await response.json();
      this.agentId = agentData.id;
      return agentData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create agent";
      this.onError?.(`Agent Creation Failed: ${errorMessage}`);
      throw error;
    }
  }

  async createChatSession(agentId: string): Promise<ChatSession> {
    try {
      const response = await this.fetchWithRetries(`${DID_API_URL}/agents/${agentId}/chat`, {
        method: "POST",
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create chat session: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const chatData: ChatSession = await response.json();
      this.chatId = chatData.id;
      return chatData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create chat session";
      this.onError?.(`Chat Session Creation Failed: ${errorMessage}`);
      throw error;
    }
  }

  async createStreamSession(): Promise<StreamSession> {
    try {
      const response = await this.fetchWithRetries(`${DID_API_URL}/clips/streams`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          presenter_id: 'v2_public_alex@qcvo4gupoy',
          driver_id: 'e3nbserss8',
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create stream session: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const streamData = await response.json();
      this.streamId = streamData.id;
      this.sessionId = streamData.session_id;

      return {
        id: streamData.id,
        offer: streamData.offer,
        ice_servers: streamData.ice_servers,
        session_id: streamData.session_id
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create stream session";
      this.onError?.(`Stream Session Creation Failed: ${errorMessage}`);
      throw error;
    }
  }

  private onIceCandidate = (event: RTCPeerConnectionIceEvent) => {
    if (event.candidate && this.streamId && this.sessionId) {
      const { candidate, sdpMid, sdpMLineIndex } = event.candidate;

      fetch(`${DID_API_URL}/clips/streams/${this.streamId}/ice`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          candidate,
          sdpMid,
          sdpMLineIndex,
          session_id: this.sessionId,
        }),
      }).catch(error => {
        console.error('Failed to send ICE candidate:', error);
      });
    }
  };

  private onTrack = (event: RTCTrackEvent) => {
    if (!event.track) return;

    this.statsIntervalId = setInterval(async () => {
      if (!this.peerConnection) return;
      
      const stats = await this.peerConnection.getStats(event.track);
      stats.forEach((report) => {
        if (report.type === 'inbound-rtp' && report.kind === 'video') {
          const videoStatusChanged = this.videoIsPlaying !== report.bytesReceived > this.lastBytesReceived;

          if (videoStatusChanged) {
            this.videoIsPlaying = report.bytesReceived > this.lastBytesReceived;
            this.onVideoStatusChange?.(this.videoIsPlaying, event.streams[0]);
          }
          this.lastBytesReceived = report.bytesReceived;
        }
      });
    }, 500);
  };

  async createPeerConnection(offer: RTCSessionDescriptionInit, iceServers: RTCIceServer[]): Promise<RTCSessionDescriptionInit> {
    if (this.peerConnection) {
      this.closePeerConnection();
    }

    this.peerConnection = new RTCPeerConnection({ iceServers });
    
    // Set up event listeners
    this.peerConnection.addEventListener('icecandidate', this.onIceCandidate);
    this.peerConnection.addEventListener('track', this.onTrack);
    this.peerConnection.addEventListener('connectionstatechange', () => {
      if (this.peerConnection) {
        this.onConnectionStateChange?.(this.peerConnection.connectionState);
        if (this.peerConnection.connectionState === 'failed' || this.peerConnection.connectionState === 'closed') {
          this.closePeerConnection();
        }
      }
    });
    this.peerConnection.addEventListener('iceconnectionstatechange', () => {
      if (this.peerConnection) {
        this.onIceConnectionStateChange?.(this.peerConnection.iceConnectionState);
        if (this.peerConnection.iceConnectionState === 'failed' || this.peerConnection.iceConnectionState === 'closed') {
          this.closePeerConnection();
        }
      }
    });

    await this.peerConnection.setRemoteDescription(offer);
    const sessionClientAnswer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(sessionClientAnswer);

    // Create data channel for agent responses
    const dataChannel = this.peerConnection.createDataChannel('JanusDataChannel');
    dataChannel.onopen = () => {
      console.log('Data channel opened');
    };

    let decodedMsg: string;
    dataChannel.onmessage = (event) => {
      let msg = event.data;
      const msgType = 'chat/answer:';
      if (msg.includes(msgType)) {
        msg = decodeURIComponent(msg.replace(msgType, ''));
        decodedMsg = msg;
        return decodedMsg;
      }
      if (msg.includes('stream/started')) {
        this.onAgentMessage?.(decodedMsg);
      }
    };

    dataChannel.onclose = () => {
      console.log('Data channel closed');
    };

    return sessionClientAnswer;
  }

  async startStream(sessionClientAnswer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.streamId || !this.sessionId) {
      throw new Error('Stream session not created');
    }

    try {
      const response = await fetch(`${DID_API_URL}/clips/streams/${this.streamId}/sdp`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          answer: sessionClientAnswer,
          session_id: this.sessionId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to start stream: ${response.status} ${response.statusText} - ${errorText}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to start stream";
      this.onError?.(`Stream Start Failed: ${errorMessage}`);
      throw error;
    }
  }

  async sendMessage(message: string): Promise<void> {
    if (!this.agentId || !this.chatId || !this.streamId || !this.sessionId) {
      throw new Error('Agent, chat session, or stream session not initialized');
    }

    try {
      const response = await this.fetchWithRetries(`${DID_API_URL}/agents/${this.agentId}/chat/${this.chatId}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          streamId: this.streamId,
          sessionId: this.sessionId,
          messages: [
            {
              role: 'user',
              content: message,
              created_at: new Date().toString(),
            },
          ],
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to send message: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const responseData = await response.json();
      if (response.status === 200 && responseData.chatMode === 'TextOnly') {
        console.log('User is out of credit, API only return text messages');
        this.onAgentMessage?.(responseData.result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to send message";
      this.onError?.(`Message Send Failed: ${errorMessage}`);
      throw error;
    }
  }

  async destroyStream(): Promise<void> {
    if (this.streamId && this.sessionId) {
      try {
        await fetch(`${DID_API_URL}/clips/streams/${this.streamId}`, {
          method: 'DELETE',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ session_id: this.sessionId }),
        });
      } catch (error) {
        console.error('Failed to destroy stream:', error);
      }
    }

    this.closePeerConnection();
    this.streamId = null;
    this.sessionId = null;
  }

  private closePeerConnection(): void {
    if (this.statsIntervalId) {
      clearInterval(this.statsIntervalId);
      this.statsIntervalId = null;
    }

    if (this.peerConnection) {
      this.peerConnection.removeEventListener('icecandidate', this.onIceCandidate);
      this.peerConnection.removeEventListener('track', this.onTrack);
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.videoIsPlaying = false;
    this.lastBytesReceived = 0;
  }

  // Utility methods
  isConnected(): boolean {
    return this.peerConnection?.connectionState === 'connected';
  }

  isStreamReady(): boolean {
    return this.streamId !== null && this.sessionId !== null;
  }

  isAgentReady(): boolean {
    return this.agentId !== null && this.chatId !== null;
  }

  getAgentId(): string | null {
    return this.agentId;
  }

  getChatId(): string | null {
    return this.chatId;
  }

  getStreamId(): string | null {
    return this.streamId;
  }

  // Clean up resources
  destroy(): void {
    this.destroyStream();
    this.agentId = null;
    this.chatId = null;
  }
}
