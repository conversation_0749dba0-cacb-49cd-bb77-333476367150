"use client";
import React from "react";
import LiveStreamingAgent from "./LiveStreamingAgent";

type CandidateWithAgentProps = {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  useAgent?: boolean;
  message?: string;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
  useStreaming?: boolean;
  avatarMode?: "standard" | "streaming" | "live";
};

const CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  useAgent = true,
  useStreaming = true,
  avatarMode = "live",
}) => {
  // If using streaming mode, render the LiveStreamingAgent
  if (useAgent && useStreaming && avatarMode === "live") {
    return (
      <LiveStreamingAgent
        className={className}
        candidateName={candidateName}
        jobTitle={jobTitle}
        showControls={true}
        showChat={false}
        autoConnect={true}
      />
    );
  }

  // Fallback to basic agent display (for backward compatibility)
  return (
    <div className={`relative ${className}`}>
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 bg-gray-400 rounded" />
          </div>
          <p className="text-sm text-gray-600">Agent not available</p>
        </div>
      </div>
    </div>
  );
};

export default CandidateWithAgent;
