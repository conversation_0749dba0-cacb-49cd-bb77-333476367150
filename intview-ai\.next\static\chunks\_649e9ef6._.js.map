{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\n\r\ntype InterviewInstructionsProps = {\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  languages?: string[];\r\n  instructions?: string[];\r\n  environmentChecklist?: string[];\r\n  disclaimers?: string[];\r\n  onNext?: () => void;\r\n};\r\n\r\nconst defaultInstructions = [\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n];\r\n\r\nconst defaultEnvironment = [\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n];\r\n\r\nconst defaultDisclaimers = [\r\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\r\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\r\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\r\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\r\n];\r\n\r\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  languages = [\"English\", \"Chinese\"],\r\n  instructions = defaultInstructions,\r\n  environmentChecklist = defaultEnvironment,\r\n  disclaimers = defaultDisclaimers,\r\n  onNext,\r\n}) => {\r\n  const [isChecked, setIsChecked] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\r\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\r\n        <p className=\"font-semibold mb-8 text-xl\">\r\n          Instructions for Interview!\r\n        </p>\r\n        <div className=\"space-y-6\">\r\n          <div>\r\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\r\n            <p className=\"text-sm mb-4\">\r\n              As part of the process you are required to complete an AI video\r\n              assessment for the role of the {jobTitle}.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {languages.map((language, index) => (\r\n                <li key={index}>{language}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {instructions.map((instruction, index) => (\r\n                <li key={index}>{instruction}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {environmentChecklist.map((item, index) => (\r\n                <li key={index}>{item}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index}>{disclaimer}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"flex items-start gap-2 mt-6\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              checked={isChecked}\r\n              onChange={(e) => setIsChecked(e.target.checked)}\r\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\r\n              By checking this box, you agree with AI Interview{\" \"}\r\n              <span className=\"text-primary cursor-pointer font-medium\">\r\n                Terms of use\r\n              </span>\r\n              .\r\n            </label>\r\n          </div>\r\n          <div className=\"flex justify-center\">\r\n            <Button\r\n              disabled={!isChecked}\r\n              variant=\"default\"\r\n              size=\"lg\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext && onNext()}\r\n            >\r\n            Proceed\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewInstructions;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,6LAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,6LAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA9FM;KAAA;uCAgGS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAoC;sDAClC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/InterviewContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from \"react\";\r\n\r\nconst DID_API_URL = \"https://api.d-id.com\";\r\n\r\ninterface Agent {\r\n  id: string;\r\n  preview_name: string;\r\n  status: string;\r\n  presenter: {\r\n    type: string;\r\n    voice: {\r\n      type: string;\r\n      voice_id: string;\r\n    };\r\n    thumbnail: string;\r\n    source_url: string;\r\n  };\r\n  llm: {\r\n    type: string;\r\n    provider: string;\r\n    model: string;\r\n    instructions: string;\r\n  };\r\n}\r\n\r\ninterface InterviewContextType {\r\n  // D-ID Agent state\r\n  agent: Agent | null;\r\n  isCreatingAgent: boolean;\r\n  agentError: string | null;\r\n  createAgent: (instructions: string, agentName: string) => Promise<void>;\r\n\r\n  // Live Streaming state\r\n  isLiveStreamingEnabled: boolean;\r\n  setIsLiveStreamingEnabled: (enabled: boolean) => void;\r\n  isStreamConnected: boolean;\r\n  setIsStreamConnected: (connected: boolean) => void;\r\n  isStreamReady: boolean;\r\n  setIsStreamReady: (ready: boolean) => void;\r\n\r\n  // Interview state\r\n  currentQuestion: number;\r\n  setCurrentQuestion: (question: number) => void;\r\n  isInterviewStarted: boolean;\r\n  setIsInterviewStarted: (started: boolean) => void;\r\n\r\n  // Questions data\r\n  questions: string[];\r\n\r\n  // Live streaming methods\r\n  speakQuestion: (questionIndex: number) => Promise<boolean>;\r\n  speakGreeting: (candidateName: string, jobTitle: string) => Promise<boolean>;\r\n}\r\n\r\nconst InterviewContext = createContext<InterviewContextType | undefined>(undefined);\r\n\r\ninterface InterviewProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {\r\n  // D-ID Agent state\r\n  const [agent, setAgent] = useState<Agent | null>(null);\r\n  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);\r\n  const [agentError, setAgentError] = useState<string | null>(null);\r\n\r\n  // Live Streaming state\r\n  const [isLiveStreamingEnabled, setIsLiveStreamingEnabled] = useState<boolean>(true);\r\n  const [isStreamConnected, setIsStreamConnected] = useState<boolean>(false);\r\n  const [isStreamReady, setIsStreamReady] = useState<boolean>(false);\r\n\r\n  // Interview state\r\n  const [currentQuestion, setCurrentQuestion] = useState<number>(1);\r\n  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);\r\n\r\n  // Questions data\r\n  const questions = [\r\n    \"Tell us about yourself and your background?\",\r\n    \"What are your key strengths and how do they relate to this position?\",\r\n    \"Why are you interested in this job and our company?\",\r\n    \"Where do you see yourself in 5 years, and how does this role fit into your career goals?\",\r\n  ];\r\n\r\n  const getAuthHeaders = () => {\r\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\";\r\n    console.log(\"Using D-ID API Key:\", apiKey ? `${apiKey.substring(0, 10)}...` : \"NOT_FOUND\");\r\n\r\n    return {\r\n      \"Authorization\": `Basic ${apiKey}`,\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n  };\r\n\r\n  const createAgent = useCallback(async (instructions: string, agentName: string) => {\r\n    // If agent already exists with same instructions, don't recreate\r\n    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {\r\n      return;\r\n    }\r\n\r\n    setIsCreatingAgent(true);\r\n    setAgentError(null);\r\n\r\n    const payload = {\r\n      presenter: {\r\n        type: \"talk\",\r\n        voice: {\r\n          type: \"microsoft\",\r\n          voice_id: \"en-US-JennyMultilingualV2Neural\"\r\n        },\r\n        thumbnail: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\",\r\n        source_url: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\"\r\n      },\r\n      llm: {\r\n        type: \"openai\",\r\n        provider: \"openai\",\r\n        model: \"gpt-4o-mini\",\r\n        instructions: instructions\r\n      },\r\n      preview_name: agentName\r\n    };\r\n\r\n    try {\r\n      console.log(\"Creating D-ID Agent with payload:\", payload);\r\n\r\n      const response = await fetch(`${DID_API_URL}/agents`, {\r\n        method: \"POST\",\r\n        headers: getAuthHeaders(),\r\n        body: JSON.stringify(payload),\r\n      });\r\n\r\n      console.log(\"D-ID Agent API Response Status:\", response.status);\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        console.error(\"D-ID Agent API Error Response:\", errorText);\r\n        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);\r\n      }\r\n\r\n      const agentData: Agent = await response.json();\r\n      console.log(\"D-ID Agent Created Successfully:\", agentData);\r\n      setAgent(agentData);\r\n    } catch (err: unknown) {\r\n      console.error(\"D-ID Agent Creation Error:\", err);\r\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create agent\";\r\n      setAgentError(`Agent Creation Failed: ${errorMessage}`);\r\n    } finally {\r\n      setIsCreatingAgent(false);\r\n    }\r\n  }, [agent]);\r\n\r\n  // Live streaming methods\r\n  const speakQuestion = useCallback(async (questionIndex: number): Promise<boolean> => {\r\n    if (!isLiveStreamingEnabled || !isStreamReady) {\r\n      console.warn('Live streaming not ready');\r\n      return false;\r\n    }\r\n\r\n    if (questionIndex < 0 || questionIndex >= questions.length) {\r\n      console.error('Invalid question index:', questionIndex);\r\n      return false;\r\n    }\r\n\r\n    const questionText = questions[questionIndex];\r\n    const enhancedQuestion = `Question ${questionIndex + 1}: ${questionText} Please take your time to think about your answer.`;\r\n\r\n    try {\r\n      // Access the global live streaming avatar instance\r\n      const avatarInstance = (window as any).liveStreamingAvatar;\r\n      if (avatarInstance && avatarInstance.speak) {\r\n        return await avatarInstance.speak(enhancedQuestion);\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Failed to speak question:', error);\r\n      return false;\r\n    }\r\n  }, [isLiveStreamingEnabled, isStreamReady, questions]);\r\n\r\n  const speakGreeting = useCallback(async (candidateName: string, jobTitle: string): Promise<boolean> => {\r\n    if (!isLiveStreamingEnabled || !isStreamReady) {\r\n      console.warn('Live streaming not ready for greeting');\r\n      return false;\r\n    }\r\n\r\n    const greetingText = `Hello ${candidateName}! Welcome to your interview for the ${jobTitle} position. I'm your AI interviewer, and I'll be asking you a few questions today. Are you ready to begin?`;\r\n\r\n    try {\r\n      // Access the global live streaming avatar instance\r\n      const avatarInstance = (window as any).liveStreamingAvatar;\r\n      if (avatarInstance && avatarInstance.speak) {\r\n        return await avatarInstance.speak(greetingText);\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Failed to speak greeting:', error);\r\n      return false;\r\n    }\r\n  }, [isLiveStreamingEnabled, isStreamReady]);\r\n\r\n  const value: InterviewContextType = {\r\n    // D-ID Agent state\r\n    agent,\r\n    isCreatingAgent,\r\n    agentError,\r\n    createAgent,\r\n\r\n    // Live Streaming state\r\n    isLiveStreamingEnabled,\r\n    setIsLiveStreamingEnabled,\r\n    isStreamConnected,\r\n    setIsStreamConnected,\r\n    isStreamReady,\r\n    setIsStreamReady,\r\n\r\n    // Interview state\r\n    currentQuestion,\r\n    setCurrentQuestion,\r\n    isInterviewStarted,\r\n    setIsInterviewStarted,\r\n\r\n    // Questions data\r\n    questions,\r\n\r\n    // Live streaming methods\r\n    speakQuestion,\r\n    speakGreeting,\r\n  };\r\n\r\n  return (\r\n    <InterviewContext.Provider value={value}>\r\n      {children}\r\n    </InterviewContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useInterview = (): InterviewContextType => {\r\n  const context = useContext(InterviewContext);\r\n  if (context === undefined) {\r\n    throw new Error('useInterview must be used within an InterviewProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;AAqFmB;;AApFnB;;;AADA;;AAGA,MAAM,cAAc;AAoDpB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,MAAM,oBAAsD,CAAC,EAAE,QAAQ,EAAE;;IAC9E,mBAAmB;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,uBAAuB;IACvB,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE5D,kBAAkB;IAClB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,iBAAiB;IACjB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,MAAM,SAAS,4FAAuC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IAAI;QACjF,QAAQ,GAAG,CAAC,uBAAuB,uCAAS,GAAG,OAAO,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;QAE3E,OAAO;YACL,iBAAiB,CAAC,MAAM,EAAE,QAAQ;YAClC,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO,cAAsB;YAC3D,iEAAiE;YACjE,IAAI,SAAS,MAAM,GAAG,CAAC,YAAY,KAAK,gBAAgB,MAAM,YAAY,KAAK,WAAW;gBACxF;YACF;YAEA,mBAAmB;YACnB,cAAc;YAEd,MAAM,UAAU;gBACd,WAAW;oBACT,MAAM;oBACN,OAAO;wBACL,MAAM;wBACN,UAAU;oBACZ;oBACA,WAAW;oBACX,YAAY;gBACd;gBACA,KAAK;oBACH,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,cAAc;gBAChB;gBACA,cAAc;YAChB;YAEA,IAAI;gBACF,QAAQ,GAAG,CAAC,qCAAqC;gBAEjD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,OAAO,CAAC,EAAE;oBACpD,QAAQ;oBACR,SAAS;oBACT,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;gBAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;gBACpG;gBAEA,MAAM,YAAmB,MAAM,SAAS,IAAI;gBAC5C,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,SAAS;YACX,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,cAAc,CAAC,uBAAuB,EAAE,cAAc;YACxD,SAAU;gBACR,mBAAmB;YACrB;QACF;qDAAG;QAAC;KAAM;IAEV,yBAAyB;IACzB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YACvC,IAAI,CAAC,0BAA0B,CAAC,eAAe;gBAC7C,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,IAAI,gBAAgB,KAAK,iBAAiB,UAAU,MAAM,EAAE;gBAC1D,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO;YACT;YAEA,MAAM,eAAe,SAAS,CAAC,cAAc;YAC7C,MAAM,mBAAmB,CAAC,SAAS,EAAE,gBAAgB,EAAE,EAAE,EAAE,aAAa,kDAAkD,CAAC;YAE3H,IAAI;gBACF,mDAAmD;gBACnD,MAAM,iBAAiB,AAAC,OAAe,mBAAmB;gBAC1D,IAAI,kBAAkB,eAAe,KAAK,EAAE;oBAC1C,OAAO,MAAM,eAAe,KAAK,CAAC;gBACpC;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO;YACT;QACF;uDAAG;QAAC;QAAwB;QAAe;KAAU;IAErD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO,eAAuB;YAC9D,IAAI,CAAC,0BAA0B,CAAC,eAAe;gBAC7C,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,MAAM,eAAe,CAAC,MAAM,EAAE,cAAc,oCAAoC,EAAE,SAAS,yGAAyG,CAAC;YAErM,IAAI;gBACF,mDAAmD;gBACnD,MAAM,iBAAiB,AAAC,OAAe,mBAAmB;gBAC1D,IAAI,kBAAkB,eAAe,KAAK,EAAE;oBAC1C,OAAO,MAAM,eAAe,KAAK,CAAC;gBACpC;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO;YACT;QACF;uDAAG;QAAC;QAAwB;KAAc;IAE1C,MAAM,QAA8B;QAClC,mBAAmB;QACnB;QACA;QACA;QACA;QAEA,uBAAuB;QACvB;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;QACA;QAEA,iBAAiB;QACjB;QAEA,yBAAyB;QACzB;QACA;IACF;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;GA7Ka;KAAA;AA+KN,MAAM,eAAe;;IAC1B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype QuestionsListProps = {\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const { questions, currentQuestion } = useInterview();\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAOA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACU;;IACnB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAElD,qBACE,6LAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,6LAAC;gCAAK,WAAU;;;;;;0CAElB,6LAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,6LAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;GA/CM;;QAGmC,+HAAA,CAAA,eAAY;;;KAH/C;uCAiDS", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/services/didLiveStreamingService.ts"], "sourcesContent": ["// D-ID Live Streaming Service\n// Based on the live-streaming-demo streaming-client-api.js\n\ninterface DIDConfig {\n  key: string;\n  url: string;\n  service: 'talks' | 'clips';\n}\n\ninterface StreamSession {\n  id: string;\n  offer: RTCSessionDescriptionInit;\n  ice_servers: RTCIceServer[];\n  session_id: string;\n}\n\ninterface ScriptConfig {\n  type: 'text' | 'audio';\n  provider?: {\n    type: string;\n    voice_id: string;\n  };\n  input?: string;\n  audio_url?: string;\n  ssml?: boolean;\n}\n\nexport class DIDLiveStreamingService {\n  private peerConnection: RTCPeerConnection | null = null;\n  private pcDataChannel: RTCDataChannel | null = null;\n  private streamId: string | null = null;\n  private sessionId: string | null = null;\n  private sessionClientAnswer: RTCSessionDescriptionInit | null = null;\n  \n  private statsIntervalId: number | null = null;\n  private lastBytesReceived: number = 0;\n  private videoIsPlaying: boolean = false;\n  private streamVideoOpacity: number = 0;\n  \n  // Stream warmup to mitigate jittering issues\n  private readonly streamWarmup: boolean = true;\n  private isStreamReady: boolean = false;\n  \n  // Video elements\n  private idleVideoElement: HTMLVideoElement | null = null;\n  private streamVideoElement: HTMLVideoElement | null = null;\n  \n  // Event callbacks\n  private onConnectionStateChange?: (state: string) => void;\n  private onStreamReady?: () => void;\n  private onStreamEvent?: (event: string, data?: any) => void;\n  private onVideoStatusChange?: (isPlaying: boolean) => void;\n  \n  private config: DIDConfig;\n  \n  constructor(config: DIDConfig) {\n    this.config = config;\n    this.isStreamReady = !this.streamWarmup;\n  }\n  \n  // Initialize video elements\n  public initializeVideoElements(\n    idleVideoElement: HTMLVideoElement,\n    streamVideoElement: HTMLVideoElement\n  ): void {\n    this.idleVideoElement = idleVideoElement;\n    this.streamVideoElement = streamVideoElement;\n    \n    // Set playsinline attribute for mobile compatibility\n    this.idleVideoElement.setAttribute('playsinline', '');\n    this.streamVideoElement.setAttribute('playsinline', '');\n  }\n  \n  // Set event callbacks\n  public setEventCallbacks(callbacks: {\n    onConnectionStateChange?: (state: string) => void;\n    onStreamReady?: () => void;\n    onStreamEvent?: (event: string, data?: any) => void;\n    onVideoStatusChange?: (isPlaying: boolean) => void;\n  }): void {\n    this.onConnectionStateChange = callbacks.onConnectionStateChange;\n    this.onStreamReady = callbacks.onStreamReady;\n    this.onStreamEvent = callbacks.onStreamEvent;\n    this.onVideoStatusChange = callbacks.onVideoStatusChange;\n  }\n  \n  // Connect to D-ID streaming service\n  public async connect(presenterConfig?: any): Promise<boolean> {\n    try {\n      console.log(\"DIDLiveStreamingService: Starting connection...\");\n\n      if (this.peerConnection && this.peerConnection.connectionState === 'connected') {\n        console.log(\"DIDLiveStreamingService: Already connected\");\n        return true;\n      }\n\n      // Clean up any existing sessions first\n      await this.destroy();\n\n      this.stopAllStreams();\n      this.closePC();\n\n      // Default presenter configuration\n      const defaultPresenterConfig = this.config.service === 'clips'\n        ? {\n            presenter_id: 'v2_public_alex@qcvo4gupoy',\n            driver_id: 'e3nbserss8',\n          }\n        : {\n            source_url: 'https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg',\n          };\n\n      const presenterInput = presenterConfig || defaultPresenterConfig;\n      console.log(\"DIDLiveStreamingService: Using presenter config:\", presenterInput);\n      \n      // Create streaming session\n      console.log(\"DIDLiveStreamingService: Creating streaming session...\");\n      const sessionResponse = await this.fetchWithRetries(`${this.config.url}/${this.config.service}/streams`, {\n        method: 'POST',\n        headers: {\n          Authorization: `Basic ${this.config.key}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...presenterInput,\n          stream_warmup: this.streamWarmup\n        }),\n      });\n\n      console.log(\"DIDLiveStreamingService: Session response status:\", sessionResponse.status);\n\n      let sessionData: StreamSession;\n\n      if (!sessionResponse.ok) {\n        const errorText = await sessionResponse.text();\n        console.error(\"DIDLiveStreamingService: Session creation failed:\", errorText);\n\n        // Handle specific error cases\n        if (sessionResponse.status === 403) {\n          const errorData = JSON.parse(errorText);\n          if (errorData.description?.includes(\"Max user sessions reached\")) {\n            console.log(\"DIDLiveStreamingService: Max sessions reached, attempting to clean up...\");\n            await this.cleanupAllSessions();\n\n            // Retry once after cleanup\n            console.log(\"DIDLiveStreamingService: Retrying session creation after cleanup...\");\n            const retryResponse = await this.fetchWithRetries(`${this.config.url}/${this.config.service}/streams`, {\n              method: 'POST',\n              headers: {\n                Authorization: `Basic ${this.config.key}`,\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({\n                ...presenterInput,\n                stream_warmup: this.streamWarmup\n              }),\n            });\n\n            if (retryResponse.ok) {\n              sessionData = await retryResponse.json();\n              console.log(\"DIDLiveStreamingService: Retry session created:\", sessionData);\n            } else {\n              throw new Error(`Failed to create stream session after cleanup: ${retryResponse.status} - ${await retryResponse.text()}`);\n            }\n          } else {\n            throw new Error(`Failed to create stream session: ${sessionResponse.status} - ${errorText}`);\n          }\n        } else {\n          throw new Error(`Failed to create stream session: ${sessionResponse.status} - ${errorText}`);\n        }\n      } else {\n        sessionData = await sessionResponse.json();\n        console.log(\"DIDLiveStreamingService: Session created:\", sessionData);\n      }\n\n      this.streamId = sessionData.id;\n      this.sessionId = sessionData.session_id;\n      \n      // Create peer connection\n      console.log(\"DIDLiveStreamingService: Creating peer connection with offer:\", sessionData.offer);\n      this.sessionClientAnswer = await this.createPeerConnection(\n        sessionData.offer,\n        sessionData.ice_servers\n      );\n\n      // Submit SDP answer\n      console.log(\"DIDLiveStreamingService: Submitting SDP answer...\");\n      const sdpResponse = await fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}/sdp`, {\n        method: 'POST',\n        headers: {\n          Authorization: `Basic ${this.config.key}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          answer: this.sessionClientAnswer,\n          session_id: this.sessionId,\n        }),\n      });\n\n      console.log(\"DIDLiveStreamingService: SDP response status:\", sdpResponse.status);\n\n      if (!sdpResponse.ok) {\n        const errorText = await sdpResponse.text();\n        console.error(\"DIDLiveStreamingService: SDP submission failed:\", errorText);\n        throw new Error(`Failed to submit SDP answer: ${sdpResponse.status} - ${errorText}`);\n      }\n\n      console.log(\"DIDLiveStreamingService: Connection established successfully\");\n      return true;\n    } catch (error) {\n      console.error('Failed to connect to D-ID streaming service:', error);\n      this.stopAllStreams();\n      this.closePC();\n      return false;\n    }\n  }\n  \n  // Speak text with the avatar\n  public async speak(text: string, voiceId: string = 'en-US-AndrewNeural'): Promise<boolean> {\n    if (!this.isConnectionReady()) {\n      console.warn('Connection not ready for streaming');\n      return false;\n    }\n    \n    const script: ScriptConfig = {\n      type: 'text',\n      provider: { \n        type: 'microsoft', \n        voice_id: voiceId \n      },\n      input: text,\n      ssml: true,\n    };\n    \n    try {\n      const response = await this.fetchWithRetries(`${this.config.url}/${this.config.service}/streams/${this.streamId}`, {\n        method: 'POST',\n        headers: {\n          Authorization: `Basic ${this.config.key}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          script,\n          config: { stitch: true },\n          session_id: this.sessionId,\n          ...(this.config.service === 'clips' && {\n            background: { color: '#FFFFFF' },\n          }),\n        }),\n      });\n      \n      return response.ok;\n    } catch (error) {\n      console.error('Failed to send speech request:', error);\n      return false;\n    }\n  }\n  \n  // Check if connection is ready for streaming\n  private isConnectionReady(): boolean {\n    return (\n      (this.peerConnection?.signalingState === 'stable' || \n       this.peerConnection?.iceConnectionState === 'connected') &&\n      this.isStreamReady\n    );\n  }\n  \n  // Destroy the streaming session\n  public async destroy(): Promise<void> {\n    if (this.streamId && this.sessionId) {\n      try {\n        console.log(\"DIDLiveStreamingService: Destroying session:\", this.streamId);\n        await fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}`, {\n          method: 'DELETE',\n          headers: {\n            Authorization: `Basic ${this.config.key}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({ session_id: this.sessionId }),\n        });\n        console.log(\"DIDLiveStreamingService: Session destroyed successfully\");\n      } catch (error) {\n        console.error('Failed to destroy stream session:', error);\n      }\n    }\n\n    this.stopAllStreams();\n    this.closePC();\n  }\n\n  // Clean up all existing sessions (to handle max sessions error)\n  private async cleanupAllSessions(): Promise<void> {\n    try {\n      console.log(\"DIDLiveStreamingService: Attempting to cleanup all sessions...\");\n\n      // Get list of active streams\n      const listResponse = await fetch(`${this.config.url}/${this.config.service}/streams`, {\n        method: 'GET',\n        headers: {\n          Authorization: `Basic ${this.config.key}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (listResponse.ok) {\n        const streams = await listResponse.json();\n        console.log(\"DIDLiveStreamingService: Found streams to cleanup:\", streams);\n\n        // Delete each stream\n        if (streams && Array.isArray(streams)) {\n          for (const stream of streams) {\n            try {\n              await fetch(`${this.config.url}/${this.config.service}/streams/${stream.id}`, {\n                method: 'DELETE',\n                headers: {\n                  Authorization: `Basic ${this.config.key}`,\n                  'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({ session_id: stream.session_id }),\n              });\n              console.log(\"DIDLiveStreamingService: Cleaned up stream:\", stream.id);\n            } catch (error) {\n              console.error(\"DIDLiveStreamingService: Failed to cleanup stream:\", stream.id, error);\n            }\n          }\n        }\n      } else {\n        console.warn(\"DIDLiveStreamingService: Could not list streams for cleanup:\", listResponse.status);\n      }\n    } catch (error) {\n      console.error(\"DIDLiveStreamingService: Error during cleanup:\", error);\n    }\n  }\n  \n  // Get connection status\n  public getConnectionStatus(): {\n    peerConnectionState: string;\n    iceConnectionState: string;\n    signalingState: string;\n    isStreamReady: boolean;\n    streamId: string | null;\n    sessionId: string | null;\n  } {\n    return {\n      peerConnectionState: this.peerConnection?.connectionState || 'new',\n      iceConnectionState: this.peerConnection?.iceConnectionState || 'new',\n      signalingState: this.peerConnection?.signalingState || 'stable',\n      isStreamReady: this.isStreamReady,\n      streamId: this.streamId,\n      sessionId: this.sessionId,\n    };\n  }\n\n  // Check if there are active sessions and clean them up if needed\n  public static async cleanupGlobalSessions(apiKey: string, service: 'talks' | 'clips' = 'clips'): Promise<void> {\n    try {\n      console.log(\"DIDLiveStreamingService: Global cleanup - checking for active sessions...\");\n\n      const listResponse = await fetch(`https://api.d-id.com/${service}/streams`, {\n        method: 'GET',\n        headers: {\n          Authorization: `Basic ${apiKey}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (listResponse.ok) {\n        const streams = await listResponse.json();\n        console.log(\"DIDLiveStreamingService: Found active sessions:\", streams);\n\n        if (streams && Array.isArray(streams) && streams.length > 0) {\n          console.log(`DIDLiveStreamingService: Cleaning up ${streams.length} active sessions...`);\n\n          for (const stream of streams) {\n            try {\n              await fetch(`https://api.d-id.com/${service}/streams/${stream.id}`, {\n                method: 'DELETE',\n                headers: {\n                  Authorization: `Basic ${apiKey}`,\n                  'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({ session_id: stream.session_id }),\n              });\n              console.log(\"DIDLiveStreamingService: Cleaned up session:\", stream.id);\n            } catch (error) {\n              console.error(\"DIDLiveStreamingService: Failed to cleanup session:\", stream.id, error);\n            }\n          }\n        } else {\n          console.log(\"DIDLiveStreamingService: No active sessions found\");\n        }\n      } else {\n        console.warn(\"DIDLiveStreamingService: Could not list sessions:\", listResponse.status);\n      }\n    } catch (error) {\n      console.error(\"DIDLiveStreamingService: Error during global cleanup:\", error);\n    }\n  }\n\n  // Private methods for WebRTC management\n  private async createPeerConnection(\n    offer: RTCSessionDescriptionInit,\n    iceServers: RTCIceServer[]\n  ): Promise<RTCSessionDescriptionInit> {\n    console.log('DIDLiveStreamingService: Creating peer connection...');\n\n    // Always create a fresh peer connection\n    if (this.peerConnection) {\n      this.closePC();\n    }\n\n    this.peerConnection = new RTCPeerConnection({ iceServers });\n    console.log('DIDLiveStreamingService: Peer connection created');\n\n    this.pcDataChannel = this.peerConnection.createDataChannel('JanusDataChannel');\n    console.log('DIDLiveStreamingService: Data channel created');\n\n    // Set up event listeners\n    this.peerConnection.addEventListener('icegatheringstatechange', this.onIceGatheringStateChange.bind(this), true);\n    this.peerConnection.addEventListener('icecandidate', this.onIceCandidate.bind(this), true);\n    this.peerConnection.addEventListener('iceconnectionstatechange', this.onIceConnectionStateChange.bind(this), true);\n    this.peerConnection.addEventListener('connectionstatechange', this.onPeerConnectionStateChange.bind(this), true);\n    this.peerConnection.addEventListener('signalingstatechange', this.onSignalingStateChange.bind(this), true);\n    this.peerConnection.addEventListener('track', this.onTrack.bind(this), true);\n    this.pcDataChannel.addEventListener('message', this.onStreamEventMessage.bind(this), true);\n\n    console.log('DIDLiveStreamingService: Event listeners set up');\n    console.log('DIDLiveStreamingService: Current signaling state:', this.peerConnection.signalingState);\n    console.log('DIDLiveStreamingService: Offer received:', offer);\n\n    // Validate the offer\n    if (!offer || !offer.sdp || offer.type !== 'offer') {\n      throw new Error('Invalid offer received from D-ID API');\n    }\n\n    console.log('DIDLiveStreamingService: Setting remote description...');\n\n    try {\n      await this.peerConnection.setRemoteDescription(offer);\n      console.log('DIDLiveStreamingService: Set remote SDP OK, signaling state:', this.peerConnection.signalingState);\n\n      // Wait a bit for the signaling state to stabilize\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n      // Check if we're in the correct state to create an answer\n      if (this.peerConnection.signalingState !== 'have-remote-offer') {\n        console.warn(`DIDLiveStreamingService: Unexpected signaling state: ${this.peerConnection.signalingState}, attempting to continue...`);\n\n        // If we're in 'stable' state, it might mean the connection is already established\n        if (this.peerConnection.signalingState === 'stable') {\n          // Return a dummy answer - this shouldn't be used\n          return { type: 'answer', sdp: '' } as RTCSessionDescriptionInit;\n        }\n      }\n\n      const sessionClientAnswer = await this.peerConnection.createAnswer();\n      console.log('DIDLiveStreamingService: Create local SDP OK');\n\n      await this.peerConnection.setLocalDescription(sessionClientAnswer);\n      console.log('DIDLiveStreamingService: Set local SDP OK, final signaling state:', this.peerConnection.signalingState);\n\n      return sessionClientAnswer;\n    } catch (error) {\n      console.error('DIDLiveStreamingService: Error in createPeerConnection:', error);\n      console.error('DIDLiveStreamingService: Signaling state at error:', this.peerConnection?.signalingState);\n      throw error;\n    }\n  }\n\n  private onIceGatheringStateChange(): void {\n    if (this.peerConnection) {\n      console.log('ICE gathering state:', this.peerConnection.iceGatheringState);\n    }\n  }\n\n  private onIceCandidate(event: RTCPeerConnectionIceEvent): void {\n    console.log('onIceCandidate', event);\n    if (event.candidate) {\n      const { candidate, sdpMid, sdpMLineIndex } = event.candidate;\n\n      fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}/ice`, {\n        method: 'POST',\n        headers: {\n          Authorization: `Basic ${this.config.key}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          candidate,\n          sdpMid,\n          sdpMLineIndex,\n          session_id: this.sessionId,\n        }),\n      }).catch(error => console.error('Failed to send ICE candidate:', error));\n    } else {\n      // For the initial 2 sec idle stream at the beginning of the connection\n      fetch(`${this.config.url}/${this.config.service}/streams/${this.streamId}/ice`, {\n        method: 'POST',\n        headers: {\n          Authorization: `Basic ${this.config.key}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          session_id: this.sessionId,\n        }),\n      }).catch(error => console.error('Failed to send null ICE candidate:', error));\n    }\n  }\n\n  private onIceConnectionStateChange(): void {\n    if (this.peerConnection) {\n      console.log('ICE connection state:', this.peerConnection.iceConnectionState);\n      if (this.peerConnection.iceConnectionState === 'failed' ||\n          this.peerConnection.iceConnectionState === 'closed' ||\n          this.peerConnection.iceConnectionState === 'disconnected') {\n        console.log('DIDLiveStreamingService: ICE connection failed, cleaning up...');\n        this.stopAllStreams();\n        this.closePC();\n      }\n    }\n  }\n\n  private onPeerConnectionStateChange(): void {\n    if (this.peerConnection) {\n      console.log('Peer connection state:', this.peerConnection.connectionState);\n      this.onConnectionStateChange?.(this.peerConnection.connectionState);\n\n      if (this.peerConnection.connectionState === 'connected') {\n        this.playIdleVideo();\n\n        // Fallback mechanism: if 'stream/ready' event isn't received within 5 seconds\n        setTimeout(() => {\n          if (!this.isStreamReady) {\n            console.log('Forcing stream/ready');\n            this.isStreamReady = true;\n            this.onStreamReady?.();\n          }\n        }, 5000);\n      }\n    }\n  }\n\n  private onSignalingStateChange(): void {\n    if (this.peerConnection) {\n      console.log('Signaling state:', this.peerConnection.signalingState);\n    }\n  }\n\n  private onTrack(event: RTCTrackEvent): void {\n    if (!event.track) return;\n\n    console.log('DIDLiveStreamingService: Track received:', event.track.kind, event.track.id);\n\n    // Clear any existing stats interval\n    if (this.statsIntervalId) {\n      clearInterval(this.statsIntervalId);\n      this.statsIntervalId = null;\n    }\n\n    this.statsIntervalId = window.setInterval(async () => {\n      if (this.peerConnection && this.peerConnection.connectionState === 'connected') {\n        try {\n          // Get stats for all receivers instead of a specific track\n          const stats = await this.peerConnection.getStats();\n          let foundVideoStats = false;\n\n          stats.forEach((report) => {\n            if (report.type === 'inbound-rtp' && report.kind === 'video') {\n              foundVideoStats = true;\n              const bytesReceived = report.bytesReceived || 0;\n              const videoStatusChanged = this.videoIsPlaying !== (bytesReceived > this.lastBytesReceived);\n\n              if (videoStatusChanged) {\n                this.videoIsPlaying = bytesReceived > this.lastBytesReceived;\n                this.handleVideoStatusChange(this.videoIsPlaying, event.streams[0]);\n              }\n              this.lastBytesReceived = bytesReceived;\n            }\n          });\n\n          // If no video stats found, assume not playing\n          if (!foundVideoStats && this.videoIsPlaying) {\n            this.videoIsPlaying = false;\n            this.handleVideoStatusChange(false);\n          }\n        } catch (error) {\n          console.warn('DIDLiveStreamingService: Error getting stats:', error);\n          // If we can't get stats, clear the interval to prevent repeated errors\n          if (this.statsIntervalId) {\n            clearInterval(this.statsIntervalId);\n            this.statsIntervalId = null;\n          }\n        }\n      }\n    }, 500);\n  }\n\n  private onStreamEventMessage(message: MessageEvent): void {\n    if (this.pcDataChannel?.readyState === 'open') {\n      const [event, _] = message.data.split(':');\n      console.log('Stream event:', event);\n\n      switch (event) {\n        case 'stream/ready':\n          setTimeout(() => {\n            console.log('stream/ready received');\n            this.isStreamReady = true;\n            this.onStreamReady?.();\n          }, 1000);\n          break;\n        case 'stream/started':\n        case 'stream/done':\n        case 'stream/error':\n          this.onStreamEvent?.(event, message.data);\n          break;\n      }\n    }\n  }\n\n  private handleVideoStatusChange(videoIsPlaying: boolean, stream?: MediaStream): void {\n    if (videoIsPlaying) {\n      this.streamVideoOpacity = this.isStreamReady ? 1 : 0;\n      this.setStreamVideoElement(stream);\n    } else {\n      this.streamVideoOpacity = 0;\n    }\n\n    if (this.streamVideoElement) {\n      this.streamVideoElement.style.opacity = this.streamVideoOpacity.toString();\n    }\n    if (this.idleVideoElement) {\n      this.idleVideoElement.style.opacity = (1 - this.streamVideoOpacity).toString();\n    }\n\n    this.onVideoStatusChange?.(videoIsPlaying);\n  }\n\n  private setStreamVideoElement(stream?: MediaStream): void {\n    if (!stream || !this.streamVideoElement) return;\n\n    this.streamVideoElement.srcObject = stream;\n    this.streamVideoElement.loop = false;\n    this.streamVideoElement.muted = !this.isStreamReady;\n\n    // Safari hotfix\n    if (this.streamVideoElement.paused) {\n      this.streamVideoElement\n        .play()\n        .then(() => {})\n        .catch((e) => console.error('Failed to play stream video:', e));\n    }\n  }\n\n  private playIdleVideo(): void {\n    if (!this.idleVideoElement) return;\n\n    const idleVideoSrc = this.config.service === 'clips'\n      ? '/alex_v2_idle.mp4'\n      : '/emma_idle.mp4';\n\n    this.idleVideoElement.src = idleVideoSrc;\n    this.idleVideoElement.loop = true;\n  }\n\n  private stopAllStreams(): void {\n    if (this.streamVideoElement?.srcObject) {\n      console.log('Stopping video streams');\n      const tracks = (this.streamVideoElement.srcObject as MediaStream).getTracks();\n      tracks.forEach((track) => track.stop());\n      this.streamVideoElement.srcObject = null;\n      this.streamVideoOpacity = 0;\n    }\n  }\n\n  private closePC(): void {\n    if (!this.peerConnection) return;\n\n    console.log('DIDLiveStreamingService: Stopping peer connection');\n\n    // Clear stats interval first\n    if (this.statsIntervalId) {\n      clearInterval(this.statsIntervalId);\n      this.statsIntervalId = null;\n      console.log('DIDLiveStreamingService: Cleared stats interval');\n    }\n\n    // Remove event listeners\n    try {\n      this.peerConnection.removeEventListener('icegatheringstatechange', this.onIceGatheringStateChange, true);\n      this.peerConnection.removeEventListener('icecandidate', this.onIceCandidate, true);\n      this.peerConnection.removeEventListener('iceconnectionstatechange', this.onIceConnectionStateChange, true);\n      this.peerConnection.removeEventListener('connectionstatechange', this.onPeerConnectionStateChange, true);\n      this.peerConnection.removeEventListener('signalingstatechange', this.onSignalingStateChange, true);\n      this.peerConnection.removeEventListener('track', this.onTrack, true);\n    } catch (error) {\n      console.warn('DIDLiveStreamingService: Error removing event listeners:', error);\n    }\n\n    // Close the peer connection\n    try {\n      this.peerConnection.close();\n    } catch (error) {\n      console.warn('DIDLiveStreamingService: Error closing peer connection:', error);\n    }\n\n    // Reset all state\n    this.isStreamReady = !this.streamWarmup;\n    this.streamVideoOpacity = 0;\n    this.videoIsPlaying = false;\n    this.lastBytesReceived = 0;\n    this.peerConnection = null;\n    this.pcDataChannel = null;\n    this.streamId = null;\n    this.sessionId = null;\n    this.sessionClientAnswer = null;\n\n    console.log('DIDLiveStreamingService: Peer connection stopped and cleaned up');\n  }\n\n  // Utility method for retrying fetch requests\n  private async fetchWithRetries(\n    url: string,\n    options: RequestInit,\n    retries: number = 1,\n    maxRetries: number = 3,\n    maxDelaySec: number = 4\n  ): Promise<Response> {\n    try {\n      return await fetch(url, options);\n    } catch (err) {\n      if (retries <= maxRetries) {\n        const delay = Math.min(Math.pow(2, retries) / 4 + Math.random(), maxDelaySec) * 1000;\n\n        await new Promise((resolve) => setTimeout(resolve, delay));\n\n        console.log(`Request failed, retrying ${retries}/${maxRetries}. Error: ${err}`);\n        return this.fetchWithRetries(url, options, retries + 1, maxRetries, maxDelaySec);\n      } else {\n        throw new Error(`Max retries exceeded. Error: ${err}`);\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,2DAA2D;;;;AA0BpD,MAAM;IACH,iBAA2C,KAAK;IAChD,gBAAuC,KAAK;IAC5C,WAA0B,KAAK;IAC/B,YAA2B,KAAK;IAChC,sBAAwD,KAAK;IAE7D,kBAAiC,KAAK;IACtC,oBAA4B,EAAE;IAC9B,iBAA0B,MAAM;IAChC,qBAA6B,EAAE;IAEvC,6CAA6C;IAC5B,eAAwB,KAAK;IACtC,gBAAyB,MAAM;IAEvC,iBAAiB;IACT,mBAA4C,KAAK;IACjD,qBAA8C,KAAK;IAE3D,kBAAkB;IACV,wBAAkD;IAClD,cAA2B;IAC3B,cAAoD;IACpD,oBAAmD;IAEnD,OAAkB;IAE1B,YAAY,MAAiB,CAAE;QAC7B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,YAAY;IACzC;IAEA,4BAA4B;IACrB,wBACL,gBAAkC,EAClC,kBAAoC,EAC9B;QACN,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,kBAAkB,GAAG;QAE1B,qDAAqD;QACrD,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,eAAe;QAClD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,eAAe;IACtD;IAEA,sBAAsB;IACf,kBAAkB,SAKxB,EAAQ;QACP,IAAI,CAAC,uBAAuB,GAAG,UAAU,uBAAuB;QAChE,IAAI,CAAC,aAAa,GAAG,UAAU,aAAa;QAC5C,IAAI,CAAC,aAAa,GAAG,UAAU,aAAa;QAC5C,IAAI,CAAC,mBAAmB,GAAG,UAAU,mBAAmB;IAC1D;IAEA,oCAAoC;IACpC,MAAa,QAAQ,eAAqB,EAAoB;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,KAAK,aAAa;gBAC9E,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,uCAAuC;YACvC,MAAM,IAAI,CAAC,OAAO;YAElB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,OAAO;YAEZ,kCAAkC;YAClC,MAAM,yBAAyB,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,UACnD;gBACE,cAAc;gBACd,WAAW;YACb,IACA;gBACE,YAAY;YACd;YAEJ,MAAM,iBAAiB,mBAAmB;YAC1C,QAAQ,GAAG,CAAC,oDAAoD;YAEhE,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,MAAM,kBAAkB,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACvG,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oBACzC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,cAAc;oBACjB,eAAe,IAAI,CAAC,YAAY;gBAClC;YACF;YAEA,QAAQ,GAAG,CAAC,qDAAqD,gBAAgB,MAAM;YAEvF,IAAI;YAEJ,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACvB,MAAM,YAAY,MAAM,gBAAgB,IAAI;gBAC5C,QAAQ,KAAK,CAAC,qDAAqD;gBAEnE,8BAA8B;gBAC9B,IAAI,gBAAgB,MAAM,KAAK,KAAK;oBAClC,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,IAAI,UAAU,WAAW,EAAE,SAAS,8BAA8B;wBAChE,QAAQ,GAAG,CAAC;wBACZ,MAAM,IAAI,CAAC,kBAAkB;wBAE7B,2BAA2B;wBAC3B,QAAQ,GAAG,CAAC;wBACZ,MAAM,gBAAgB,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;4BACrG,QAAQ;4BACR,SAAS;gCACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;gCACzC,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;gCACnB,GAAG,cAAc;gCACjB,eAAe,IAAI,CAAC,YAAY;4BAClC;wBACF;wBAEA,IAAI,cAAc,EAAE,EAAE;4BACpB,cAAc,MAAM,cAAc,IAAI;4BACtC,QAAQ,GAAG,CAAC,mDAAmD;wBACjE,OAAO;4BACL,MAAM,IAAI,MAAM,CAAC,+CAA+C,EAAE,cAAc,MAAM,CAAC,GAAG,EAAE,MAAM,cAAc,IAAI,IAAI;wBAC1H;oBACF,OAAO;wBACL,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,gBAAgB,MAAM,CAAC,GAAG,EAAE,WAAW;oBAC7F;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,gBAAgB,MAAM,CAAC,GAAG,EAAE,WAAW;gBAC7F;YACF,OAAO;gBACL,cAAc,MAAM,gBAAgB,IAAI;gBACxC,QAAQ,GAAG,CAAC,6CAA6C;YAC3D;YAEA,IAAI,CAAC,QAAQ,GAAG,YAAY,EAAE;YAC9B,IAAI,CAAC,SAAS,GAAG,YAAY,UAAU;YAEvC,yBAAyB;YACzB,QAAQ,GAAG,CAAC,iEAAiE,YAAY,KAAK;YAC9F,IAAI,CAAC,mBAAmB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CACxD,YAAY,KAAK,EACjB,YAAY,WAAW;YAGzB,oBAAoB;YACpB,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACxG,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oBACzC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,IAAI,CAAC,mBAAmB;oBAChC,YAAY,IAAI,CAAC,SAAS;gBAC5B;YACF;YAEA,QAAQ,GAAG,CAAC,iDAAiD,YAAY,MAAM;YAE/E,IAAI,CAAC,YAAY,EAAE,EAAE;gBACnB,MAAM,YAAY,MAAM,YAAY,IAAI;gBACxC,QAAQ,KAAK,CAAC,mDAAmD;gBACjE,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,YAAY,MAAM,CAAC,GAAG,EAAE,WAAW;YACrF;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,OAAO;YACZ,OAAO;QACT;IACF;IAEA,6BAA6B;IAC7B,MAAa,MAAM,IAAY,EAAE,UAAkB,oBAAoB,EAAoB;QACzF,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI;YAC7B,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,MAAM,SAAuB;YAC3B,MAAM;YACN,UAAU;gBACR,MAAM;gBACN,UAAU;YACZ;YACA,OAAO;YACP,MAAM;QACR;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACjH,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oBACzC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ;wBAAE,QAAQ;oBAAK;oBACvB,YAAY,IAAI,CAAC,SAAS;oBAC1B,GAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,WAAW;wBACrC,YAAY;4BAAE,OAAO;wBAAU;oBACjC,CAAC;gBACH;YACF;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA,6CAA6C;IACrC,oBAA6B;QACnC,OACE,CAAC,IAAI,CAAC,cAAc,EAAE,mBAAmB,YACxC,IAAI,CAAC,cAAc,EAAE,uBAAuB,WAAW,KACxD,IAAI,CAAC,aAAa;IAEtB;IAEA,gCAAgC;IAChC,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI;gBACF,QAAQ,GAAG,CAAC,gDAAgD,IAAI,CAAC,QAAQ;gBACzE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAChF,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;wBACzC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE,YAAY,IAAI,CAAC,SAAS;oBAAC;gBACpD;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;QAEA,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,OAAO;IACd;IAEA,gEAAgE;IAChE,MAAc,qBAAoC;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,6BAA6B;YAC7B,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oBACzC,gBAAgB;gBAClB;YACF;YAEA,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,UAAU,MAAM,aAAa,IAAI;gBACvC,QAAQ,GAAG,CAAC,sDAAsD;gBAElE,qBAAqB;gBACrB,IAAI,WAAW,MAAM,OAAO,CAAC,UAAU;oBACrC,KAAK,MAAM,UAAU,QAAS;wBAC5B,IAAI;4BACF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE;gCAC5E,QAAQ;gCACR,SAAS;oCACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oCACzC,gBAAgB;gCAClB;gCACA,MAAM,KAAK,SAAS,CAAC;oCAAE,YAAY,OAAO,UAAU;gCAAC;4BACvD;4BACA,QAAQ,GAAG,CAAC,+CAA+C,OAAO,EAAE;wBACtE,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,sDAAsD,OAAO,EAAE,EAAE;wBACjF;oBACF;gBACF;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,gEAAgE,aAAa,MAAM;YAClG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;QAClE;IACF;IAEA,wBAAwB;IACjB,sBAOL;QACA,OAAO;YACL,qBAAqB,IAAI,CAAC,cAAc,EAAE,mBAAmB;YAC7D,oBAAoB,IAAI,CAAC,cAAc,EAAE,sBAAsB;YAC/D,gBAAgB,IAAI,CAAC,cAAc,EAAE,kBAAkB;YACvD,eAAe,IAAI,CAAC,aAAa;YACjC,UAAU,IAAI,CAAC,QAAQ;YACvB,WAAW,IAAI,CAAC,SAAS;QAC3B;IACF;IAEA,iEAAiE;IACjE,aAAoB,sBAAsB,MAAc,EAAE,UAA6B,OAAO,EAAiB;QAC7G,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,eAAe,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ,QAAQ,CAAC,EAAE;gBAC1E,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,QAAQ;oBAChC,gBAAgB;gBAClB;YACF;YAEA,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,UAAU,MAAM,aAAa,IAAI;gBACvC,QAAQ,GAAG,CAAC,mDAAmD;gBAE/D,IAAI,WAAW,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,GAAG,GAAG;oBAC3D,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,MAAM,CAAC,mBAAmB,CAAC;oBAEvF,KAAK,MAAM,UAAU,QAAS;wBAC5B,IAAI;4BACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE;gCAClE,QAAQ;gCACR,SAAS;oCACP,eAAe,CAAC,MAAM,EAAE,QAAQ;oCAChC,gBAAgB;gCAClB;gCACA,MAAM,KAAK,SAAS,CAAC;oCAAE,YAAY,OAAO,UAAU;gCAAC;4BACvD;4BACA,QAAQ,GAAG,CAAC,gDAAgD,OAAO,EAAE;wBACvE,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,uDAAuD,OAAO,EAAE,EAAE;wBAClF;oBACF;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,qDAAqD,aAAa,MAAM;YACvF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yDAAyD;QACzE;IACF;IAEA,wCAAwC;IACxC,MAAc,qBACZ,KAAgC,EAChC,UAA0B,EACU;QACpC,QAAQ,GAAG,CAAC;QAEZ,wCAAwC;QACxC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,OAAO;QACd;QAEA,IAAI,CAAC,cAAc,GAAG,IAAI,kBAAkB;YAAE;QAAW;QACzD,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;QAC3D,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,2BAA2B,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,GAAG;QAC3G,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,GAAG;QACrF,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,4BAA4B,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,GAAG;QAC7G,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,yBAAyB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,GAAG;QAC3G,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,wBAAwB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,GAAG;QACrG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG;QACvE,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,WAAW,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG;QAErF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qDAAqD,IAAI,CAAC,cAAc,CAAC,cAAc;QACnG,QAAQ,GAAG,CAAC,4CAA4C;QAExD,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,SAAS;YAClD,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC;YAC/C,QAAQ,GAAG,CAAC,gEAAgE,IAAI,CAAC,cAAc,CAAC,cAAc;YAE9G,kDAAkD;YAClD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0DAA0D;YAC1D,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,KAAK,qBAAqB;gBAC9D,QAAQ,IAAI,CAAC,CAAC,qDAAqD,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,2BAA2B,CAAC;gBAEpI,kFAAkF;gBAClF,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,KAAK,UAAU;oBACnD,iDAAiD;oBACjD,OAAO;wBAAE,MAAM;wBAAU,KAAK;oBAAG;gBACnC;YACF;YAEA,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY;YAClE,QAAQ,GAAG,CAAC;YAEZ,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC;YAC9C,QAAQ,GAAG,CAAC,qEAAqE,IAAI,CAAC,cAAc,CAAC,cAAc;YAEnH,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2DAA2D;YACzE,QAAQ,KAAK,CAAC,sDAAsD,IAAI,CAAC,cAAc,EAAE;YACzF,MAAM;QACR;IACF;IAEQ,4BAAkC;QACxC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,GAAG,CAAC,wBAAwB,IAAI,CAAC,cAAc,CAAC,iBAAiB;QAC3E;IACF;IAEQ,eAAe,KAAgC,EAAQ;QAC7D,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,IAAI,MAAM,SAAS,EAAE;YACnB,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,SAAS;YAE5D,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oBACzC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA,YAAY,IAAI,CAAC,SAAS;gBAC5B;YACF,GAAG,KAAK,CAAC,CAAA,QAAS,QAAQ,KAAK,CAAC,iCAAiC;QACnE,OAAO;YACL,uEAAuE;YACvE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;oBACzC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,IAAI,CAAC,SAAS;gBAC5B;YACF,GAAG,KAAK,CAAC,CAAA,QAAS,QAAQ,KAAK,CAAC,sCAAsC;QACxE;IACF;IAEQ,6BAAmC;QACzC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,GAAG,CAAC,yBAAyB,IAAI,CAAC,cAAc,CAAC,kBAAkB;YAC3E,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,KAAK,YAC3C,IAAI,CAAC,cAAc,CAAC,kBAAkB,KAAK,YAC3C,IAAI,CAAC,cAAc,CAAC,kBAAkB,KAAK,gBAAgB;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,cAAc;gBACnB,IAAI,CAAC,OAAO;YACd;QACF;IACF;IAEQ,8BAAoC;QAC1C,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,GAAG,CAAC,0BAA0B,IAAI,CAAC,cAAc,CAAC,eAAe;YACzE,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe;YAElE,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,KAAK,aAAa;gBACvD,IAAI,CAAC,aAAa;gBAElB,8EAA8E;gBAC9E,WAAW;oBACT,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;wBACvB,QAAQ,GAAG,CAAC;wBACZ,IAAI,CAAC,aAAa,GAAG;wBACrB,IAAI,CAAC,aAAa;oBACpB;gBACF,GAAG;YACL;QACF;IACF;IAEQ,yBAA+B;QACrC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,GAAG,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC,cAAc;QACpE;IACF;IAEQ,QAAQ,KAAoB,EAAQ;QAC1C,IAAI,CAAC,MAAM,KAAK,EAAE;QAElB,QAAQ,GAAG,CAAC,4CAA4C,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC,EAAE;QAExF,oCAAoC;QACpC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,cAAc,IAAI,CAAC,eAAe;YAClC,IAAI,CAAC,eAAe,GAAG;QACzB;QAEA,IAAI,CAAC,eAAe,GAAG,OAAO,WAAW,CAAC;YACxC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,KAAK,aAAa;gBAC9E,IAAI;oBACF,0DAA0D;oBAC1D,MAAM,QAAQ,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ;oBAChD,IAAI,kBAAkB;oBAEtB,MAAM,OAAO,CAAC,CAAC;wBACb,IAAI,OAAO,IAAI,KAAK,iBAAiB,OAAO,IAAI,KAAK,SAAS;4BAC5D,kBAAkB;4BAClB,MAAM,gBAAgB,OAAO,aAAa,IAAI;4BAC9C,MAAM,qBAAqB,IAAI,CAAC,cAAc,KAAM,gBAAgB,IAAI,CAAC,iBAAiB;4BAE1F,IAAI,oBAAoB;gCACtB,IAAI,CAAC,cAAc,GAAG,gBAAgB,IAAI,CAAC,iBAAiB;gCAC5D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,OAAO,CAAC,EAAE;4BACpE;4BACA,IAAI,CAAC,iBAAiB,GAAG;wBAC3B;oBACF;oBAEA,8CAA8C;oBAC9C,IAAI,CAAC,mBAAmB,IAAI,CAAC,cAAc,EAAE;wBAC3C,IAAI,CAAC,cAAc,GAAG;wBACtB,IAAI,CAAC,uBAAuB,CAAC;oBAC/B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,iDAAiD;oBAC9D,uEAAuE;oBACvE,IAAI,IAAI,CAAC,eAAe,EAAE;wBACxB,cAAc,IAAI,CAAC,eAAe;wBAClC,IAAI,CAAC,eAAe,GAAG;oBACzB;gBACF;YACF;QACF,GAAG;IACL;IAEQ,qBAAqB,OAAqB,EAAQ;QACxD,IAAI,IAAI,CAAC,aAAa,EAAE,eAAe,QAAQ;YAC7C,MAAM,CAAC,OAAO,EAAE,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC;YACtC,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,OAAQ;gBACN,KAAK;oBACH,WAAW;wBACT,QAAQ,GAAG,CAAC;wBACZ,IAAI,CAAC,aAAa,GAAG;wBACrB,IAAI,CAAC,aAAa;oBACpB,GAAG;oBACH;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,aAAa,GAAG,OAAO,QAAQ,IAAI;oBACxC;YACJ;QACF;IACF;IAEQ,wBAAwB,cAAuB,EAAE,MAAoB,EAAQ;QACnF,IAAI,gBAAgB;YAClB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI;YACnD,IAAI,CAAC,qBAAqB,CAAC;QAC7B,OAAO;YACL,IAAI,CAAC,kBAAkB,GAAG;QAC5B;QAEA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC1E;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,QAAQ;QAC9E;QAEA,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IAEQ,sBAAsB,MAAoB,EAAQ;QACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAEzC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG;QACpC,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG;QAC/B,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,aAAa;QAEnD,gBAAgB;QAChB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YAClC,IAAI,CAAC,kBAAkB,CACpB,IAAI,GACJ,IAAI,CAAC,KAAO,GACZ,KAAK,CAAC,CAAC,IAAM,QAAQ,KAAK,CAAC,gCAAgC;QAChE;IACF;IAEQ,gBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,MAAM,eAAe,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,UACzC,sBACA;QAEJ,IAAI,CAAC,gBAAgB,CAAC,GAAG,GAAG;QAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG;IAC/B;IAEQ,iBAAuB;QAC7B,IAAI,IAAI,CAAC,kBAAkB,EAAE,WAAW;YACtC,QAAQ,GAAG,CAAC;YACZ,MAAM,SAAS,AAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAiB,SAAS;YAC3E,OAAO,OAAO,CAAC,CAAC,QAAU,MAAM,IAAI;YACpC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG;YACpC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;IACF;IAEQ,UAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QAE1B,QAAQ,GAAG,CAAC;QAEZ,6BAA6B;QAC7B,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,cAAc,IAAI,CAAC,eAAe;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,QAAQ,GAAG,CAAC;QACd;QAEA,yBAAyB;QACzB,IAAI;YACF,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,2BAA2B,IAAI,CAAC,yBAAyB,EAAE;YACnG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,gBAAgB,IAAI,CAAC,cAAc,EAAE;YAC7E,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,4BAA4B,IAAI,CAAC,0BAA0B,EAAE;YACrG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,yBAAyB,IAAI,CAAC,2BAA2B,EAAE;YACnG,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,wBAAwB,IAAI,CAAC,sBAAsB,EAAE;YAC7F,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,4DAA4D;QAC3E;QAEA,4BAA4B;QAC5B,IAAI;YACF,IAAI,CAAC,cAAc,CAAC,KAAK;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,2DAA2D;QAC1E;QAEA,kBAAkB;QAClB,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,YAAY;QACvC,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,mBAAmB,GAAG;QAE3B,QAAQ,GAAG,CAAC;IACd;IAEA,6CAA6C;IAC7C,MAAc,iBACZ,GAAW,EACX,OAAoB,EACpB,UAAkB,CAAC,EACnB,aAAqB,CAAC,EACtB,cAAsB,CAAC,EACJ;QACnB,IAAI;YACF,OAAO,MAAM,MAAM,KAAK;QAC1B,EAAE,OAAO,KAAK;YACZ,IAAI,WAAW,YAAY;gBACzB,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,KAAK,MAAM,IAAI,eAAe;gBAEhF,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,EAAE,WAAW,SAAS,EAAE,KAAK;gBAC9E,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,SAAS,UAAU,GAAG,YAAY;YACtE,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK;YACvD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/LiveStreamingAvatar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef, useState, useCallback } from \"react\";\nimport { Bo<PERSON>, Loader2, Wifi, WifiOff } from \"lucide-react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { DIDLiveStreamingService } from \"@/services/didLiveStreamingService\";\n\ninterface LiveStreamingAvatarProps {\n  className?: string;\n  candidateName?: string;\n  jobTitle?: string;\n  onConnectionReady?: () => void;\n  onStreamReady?: () => void;\n  onSpeechStart?: () => void;\n  onSpeechEnd?: () => void;\n  autoConnect?: boolean;\n  greetingMessage?: string;\n}\n\ninterface ConnectionStatus {\n  peerConnectionState: string;\n  iceConnectionState: string;\n  signalingState: string;\n  isStreamReady: boolean;\n}\n\nconst LiveStreamingAvatar: React.FC<LiveStreamingAvatarProps> = ({\n  className = \"\",\n  candidateName = \"Jonathan\",\n  jobTitle = \"Insurance Agent\",\n  onConnectionReady,\n  onStreamReady,\n  onSpeechStart,\n  onSpeechEnd,\n  autoConnect = true,\n  greetingMessage,\n}) => {\n  const idleVideoRef = useRef<HTMLVideoElement>(null);\n  const streamVideoRef = useRef<HTMLVideoElement>(null);\n  const streamingServiceRef = useRef<DIDLiveStreamingService | null>(null);\n  \n  const [isConnecting, setIsConnecting] = useState<boolean>(false);\n  const [isConnected, setIsConnected] = useState<boolean>(false);\n  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({\n    peerConnectionState: 'new',\n    iceConnectionState: 'new',\n    signalingState: 'stable',\n    isStreamReady: false,\n  });\n  const [error, setError] = useState<string | null>(null);\n  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);\n\n  // Initialize the streaming service\n  const initializeStreamingService = useCallback(() => {\n    // Try to get API key from multiple sources\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY ||\n                   process.env.DID_API_KEY ||\n                   \"******************************:rMMu3KcLvThOQcPwNtrcl\"; // Fallback from api.json\n\n    console.log(\"Initializing D-ID streaming service with API key:\", apiKey ? `${apiKey.substring(0, 10)}...` : \"NOT_FOUND\");\n\n    if (!apiKey) {\n      setError(\"D-ID API key not found\");\n      return null;\n    }\n\n    const config = {\n      key: apiKey,\n      url: \"https://api.d-id.com\",\n      service: \"clips\" as const, // Use clips for high-quality avatars\n    };\n\n    const service = new DIDLiveStreamingService(config);\n    \n    // Set up event callbacks\n    service.setEventCallbacks({\n      onConnectionStateChange: (state: string) => {\n        console.log(\"Connection state changed:\", state);\n        setConnectionStatus(prev => ({ ...prev, peerConnectionState: state }));\n        \n        if (state === 'connected') {\n          setIsConnected(true);\n          setIsConnecting(false);\n          onConnectionReady?.();\n        } else if (state === 'failed' || state === 'closed') {\n          setIsConnected(false);\n          setIsConnecting(false);\n          setError(\"Connection failed\");\n        }\n      },\n      onStreamReady: () => {\n        console.log(\"Stream ready\");\n        setConnectionStatus(prev => ({ ...prev, isStreamReady: true }));\n        onStreamReady?.();\n        \n        // Speak greeting message if provided\n        if (greetingMessage && streamingServiceRef.current) {\n          console.log(\"LiveStreamingAvatar: Scheduling greeting message:\", greetingMessage);\n          setTimeout(() => {\n            console.log(\"LiveStreamingAvatar: Speaking greeting message\");\n            speakText(greetingMessage);\n          }, 2000); // Increased delay to ensure connection is fully ready\n        }\n      },\n      onStreamEvent: (event: string, data?: any) => {\n        console.log(\"Stream event:\", event, data);\n        \n        if (event === 'stream/started') {\n          setIsSpeaking(true);\n          onSpeechStart?.();\n        } else if (event === 'stream/done') {\n          setIsSpeaking(false);\n          onSpeechEnd?.();\n        }\n      },\n      onVideoStatusChange: (isPlaying: boolean) => {\n        console.log(\"Video status changed:\", isPlaying);\n      },\n    });\n\n    return service;\n  }, [onConnectionReady, onStreamReady, onSpeechStart, onSpeechEnd, greetingMessage]);\n\n  // Connect to the streaming service\n  const connect = useCallback(async () => {\n    if (isConnecting || isConnected) {\n      console.log(\"Already connecting or connected, skipping...\");\n      return;\n    }\n\n    console.log(\"Starting connection to D-ID streaming service...\");\n    setIsConnecting(true);\n    setError(null);\n\n    try {\n      const service = streamingServiceRef.current || initializeStreamingService();\n      if (!service) {\n        throw new Error(\"Failed to initialize streaming service\");\n      }\n\n      streamingServiceRef.current = service;\n      console.log(\"Streaming service initialized\");\n\n      // Wait for video elements to be available\n      if (!idleVideoRef.current || !streamVideoRef.current) {\n        console.log(\"Waiting for video elements...\");\n        // Wait a bit for the DOM to be ready\n        await new Promise(resolve => setTimeout(resolve, 100));\n      }\n\n      // Initialize video elements\n      if (idleVideoRef.current && streamVideoRef.current) {\n        console.log(\"Initializing video elements\");\n        service.initializeVideoElements(idleVideoRef.current, streamVideoRef.current);\n      } else {\n        console.warn(\"Video elements not found\");\n      }\n\n      // Connect to D-ID streaming service\n      console.log(\"Connecting to D-ID streaming service...\");\n      const success = await service.connect();\n\n      if (!success) {\n        throw new Error(\"Failed to connect to streaming service\");\n      }\n\n      console.log(\"Successfully connected to D-ID streaming service\");\n\n    } catch (err) {\n      console.error(\"Connection error:\", err);\n      setError(err instanceof Error ? err.message : \"Connection failed\");\n      setIsConnecting(false);\n    }\n  }, [isConnecting, isConnected, initializeStreamingService]);\n\n  // Speak text using the avatar\n  const speakText = useCallback(async (text: string, voiceId?: string) => {\n    if (!streamingServiceRef.current || !connectionStatus.isStreamReady) {\n      console.warn(\"Cannot speak: streaming service not ready\");\n      return false;\n    }\n    \n    try {\n      const success = await streamingServiceRef.current.speak(text, voiceId);\n      return success;\n    } catch (error) {\n      console.error(\"Failed to speak text:\", error);\n      return false;\n    }\n  }, [connectionStatus.isStreamReady]);\n\n  // Disconnect from the streaming service\n  const disconnect = useCallback(async () => {\n    if (streamingServiceRef.current) {\n      await streamingServiceRef.current.destroy();\n      streamingServiceRef.current = null;\n    }\n    \n    setIsConnected(false);\n    setIsConnecting(false);\n    setConnectionStatus({\n      peerConnectionState: 'new',\n      iceConnectionState: 'new',\n      signalingState: 'stable',\n      isStreamReady: false,\n    });\n  }, []);\n\n  // Auto-connect on mount\n  useEffect(() => {\n    if (autoConnect) {\n      // Clean up any existing sessions first, then connect\n      const initializeConnection = async () => {\n        try {\n          const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY ||\n                         process.env.DID_API_KEY ||\n                         \"******************************:rMMu3KcLvThOQcPwNtrcl\";\n\n          if (apiKey) {\n            await DIDLiveStreamingService.cleanupGlobalSessions(apiKey, \"clips\");\n          }\n\n          // Add a small delay to ensure cleanup is complete\n          setTimeout(() => {\n            connect();\n          }, 1000);\n        } catch (error) {\n          console.error(\"Failed to cleanup sessions:\", error);\n          // Still try to connect even if cleanup fails\n          setTimeout(() => {\n            connect();\n          }, 1000);\n        }\n      };\n\n      initializeConnection();\n    }\n  }, [autoConnect, connect]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      console.log(\"LiveStreamingAvatar: Component unmounting, cleaning up...\");\n      disconnect();\n    };\n  }, [disconnect]);\n\n  // Expose methods to parent component\n  useEffect(() => {\n    // You can expose these methods to parent components if needed\n    (window as any).liveStreamingAvatar = {\n      speak: speakText,\n      connect,\n      disconnect,\n      getStatus: () => connectionStatus,\n    };\n  }, [speakText, connect, disconnect, connectionStatus]);\n\n  const getConnectionStatusColor = () => {\n    if (isConnected && connectionStatus.isStreamReady) return \"text-green-500\";\n    if (isConnecting) return \"text-yellow-500\";\n    if (error) return \"text-red-500\";\n    return \"text-gray-500\";\n  };\n\n  const getConnectionStatusIcon = () => {\n    if (isConnected && connectionStatus.isStreamReady) return <Wifi className=\"w-4 h-4\" />;\n    if (isConnecting) return <Loader2 className=\"w-4 h-4 animate-spin\" />;\n    return <WifiOff className=\"w-4 h-4\" />;\n  };\n\n  return (\n    <div className={`relative ${className}`}>\n      <div className=\"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden\">\n        \n        {/* Connection Status Indicator */}\n        <div className=\"absolute top-4 right-4 z-10\">\n          <div className={`flex items-center gap-2 px-3 py-1 rounded-full bg-white/80 backdrop-blur-sm ${getConnectionStatusColor()}`}>\n            {getConnectionStatusIcon()}\n            <span className=\"text-xs font-medium\">\n              {isConnected && connectionStatus.isStreamReady ? \"Live\" : \n               isConnecting ? \"Connecting...\" : \n               error ? \"Error\" : \"Offline\"}\n            </span>\n          </div>\n        </div>\n\n        {/* Speaking Indicator */}\n        <AnimatePresence>\n          {isSpeaking && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              className=\"absolute top-4 left-4 z-10\"\n            >\n              <div className=\"flex items-center gap-2 px-3 py-1 rounded-full bg-green-500/80 backdrop-blur-sm text-white\">\n                <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\" />\n                <span className=\"text-xs font-medium\">Speaking</span>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Video Container */}\n        <div className=\"relative w-full h-full flex items-center justify-center\">\n          {/* Idle Video (shown when not streaming) */}\n          <video\n            ref={idleVideoRef}\n            className=\"absolute inset-0 w-full h-full object-cover rounded-2xl\"\n            style={{ opacity: 1 }}\n            autoPlay\n            loop\n            muted\n            playsInline\n          />\n          \n          {/* Stream Video (shown when streaming) */}\n          <video\n            ref={streamVideoRef}\n            className=\"absolute inset-0 w-full h-full object-cover rounded-2xl\"\n            style={{ opacity: 0 }}\n            autoPlay\n            playsInline\n          />\n          \n          {/* Fallback content when not connected */}\n          {!isConnected && !isConnecting && (\n            <div className=\"absolute inset-0 flex flex-col items-center justify-center bg-gray-100 rounded-2xl\">\n              <Bot className=\"w-16 h-16 text-gray-400 mb-4\" />\n              <p className=\"text-gray-600 text-center px-4\">\n                {error ? `Error: ${error}` : \"Avatar not connected\"}\n              </p>\n              {!autoConnect && (\n                <button\n                  onClick={connect}\n                  className=\"mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n                >\n                  Connect Avatar\n                </button>\n              )}\n            </div>\n          )}\n          \n          {/* Loading overlay */}\n          {isConnecting && (\n            <div className=\"absolute inset-0 flex flex-col items-center justify-center bg-black/20 rounded-2xl\">\n              <Loader2 className=\"w-8 h-8 text-white animate-spin mb-2\" />\n              <p className=\"text-white text-sm\">Connecting to avatar...</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LiveStreamingAvatar;\n"], "names": [], "mappings": ";;;AAsDmB;;AArDnB;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAJA;;;;;AAyBA,MAAM,sBAA0D,CAAC,EAC/D,YAAY,EAAE,EACd,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,WAAW,EACX,cAAc,IAAI,EAClB,eAAe,EAChB;;IACC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkC;IAEnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,qBAAqB;QACrB,oBAAoB;QACpB,gBAAgB;QAChB,eAAe;IACjB;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEtD,mCAAmC;IACnC,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uEAAE;YAC7C,2CAA2C;YAC3C,MAAM,SAAS,4FACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IACvB,wDAAwD,yBAAyB;YAEhG,QAAQ,GAAG,CAAC,qDAAqD,uCAAS,GAAG,OAAO,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;YAEzG,uCAAa;;YAGb;YAEA,MAAM,SAAS;gBACb,KAAK;gBACL,KAAK;gBACL,SAAS;YACX;YAEA,MAAM,UAAU,IAAI,sIAAA,CAAA,0BAAuB,CAAC;YAE5C,yBAAyB;YACzB,QAAQ,iBAAiB,CAAC;gBACxB,uBAAuB;mFAAE,CAAC;wBACxB,QAAQ,GAAG,CAAC,6BAA6B;wBACzC;2FAAoB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,qBAAqB;gCAAM,CAAC;;wBAEpE,IAAI,UAAU,aAAa;4BACzB,eAAe;4BACf,gBAAgB;4BAChB;wBACF,OAAO,IAAI,UAAU,YAAY,UAAU,UAAU;4BACnD,eAAe;4BACf,gBAAgB;4BAChB,SAAS;wBACX;oBACF;;gBACA,aAAa;mFAAE;wBACb,QAAQ,GAAG,CAAC;wBACZ;2FAAoB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,eAAe;gCAAK,CAAC;;wBAC7D;wBAEA,qCAAqC;wBACrC,IAAI,mBAAmB,oBAAoB,OAAO,EAAE;4BAClD,QAAQ,GAAG,CAAC,qDAAqD;4BACjE;+FAAW;oCACT,QAAQ,GAAG,CAAC;oCACZ,UAAU;gCACZ;8FAAG,OAAO,sDAAsD;wBAClE;oBACF;;gBACA,aAAa;mFAAE,CAAC,OAAe;wBAC7B,QAAQ,GAAG,CAAC,iBAAiB,OAAO;wBAEpC,IAAI,UAAU,kBAAkB;4BAC9B,cAAc;4BACd;wBACF,OAAO,IAAI,UAAU,eAAe;4BAClC,cAAc;4BACd;wBACF;oBACF;;gBACA,mBAAmB;mFAAE,CAAC;wBACpB,QAAQ,GAAG,CAAC,yBAAyB;oBACvC;;YACF;YAEA,OAAO;QACT;sEAAG;QAAC;QAAmB;QAAe;QAAe;QAAa;KAAgB;IAElF,mCAAmC;IACnC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC1B,IAAI,gBAAgB,aAAa;gBAC/B,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,SAAS;YAET,IAAI;gBACF,MAAM,UAAU,oBAAoB,OAAO,IAAI;gBAC/C,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,MAAM;gBAClB;gBAEA,oBAAoB,OAAO,GAAG;gBAC9B,QAAQ,GAAG,CAAC;gBAEZ,0CAA0C;gBAC1C,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,eAAe,OAAO,EAAE;oBACpD,QAAQ,GAAG,CAAC;oBACZ,qCAAqC;oBACrC,MAAM,IAAI;oEAAQ,CAAA,UAAW,WAAW,SAAS;;gBACnD;gBAEA,4BAA4B;gBAC5B,IAAI,aAAa,OAAO,IAAI,eAAe,OAAO,EAAE;oBAClD,QAAQ,GAAG,CAAC;oBACZ,QAAQ,uBAAuB,CAAC,aAAa,OAAO,EAAE,eAAe,OAAO;gBAC9E,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACf;gBAEA,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;gBACZ,MAAM,UAAU,MAAM,QAAQ,OAAO;gBAErC,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC;YAEd,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC9C,gBAAgB;YAClB;QACF;mDAAG;QAAC;QAAc;QAAa;KAA2B;IAE1D,8BAA8B;IAC9B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO,MAAc;YACjD,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,iBAAiB,aAAa,EAAE;gBACnE,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,UAAU,MAAM,oBAAoB,OAAO,CAAC,KAAK,CAAC,MAAM;gBAC9D,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO;YACT;QACF;qDAAG;QAAC,iBAAiB,aAAa;KAAC;IAEnC,wCAAwC;IACxC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC7B,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,MAAM,oBAAoB,OAAO,CAAC,OAAO;gBACzC,oBAAoB,OAAO,GAAG;YAChC;YAEA,eAAe;YACf,gBAAgB;YAChB,oBAAoB;gBAClB,qBAAqB;gBACrB,oBAAoB;gBACpB,gBAAgB;gBAChB,eAAe;YACjB;QACF;sDAAG,EAAE;IAEL,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,aAAa;gBACf,qDAAqD;gBACrD,MAAM;0EAAuB;wBAC3B,IAAI;4BACF,MAAM,SAAS,4FACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IACvB;4BAEf,wCAAY;gCACV,MAAM,sIAAA,CAAA,0BAAuB,CAAC,qBAAqB,CAAC,QAAQ;4BAC9D;4BAEA,kDAAkD;4BAClD;sFAAW;oCACT;gCACF;qFAAG;wBACL,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,+BAA+B;4BAC7C,6CAA6C;4BAC7C;sFAAW;oCACT;gCACF;qFAAG;wBACL;oBACF;;gBAEA;YACF;QACF;wCAAG;QAAC;QAAa;KAAQ;IAEzB,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR;iDAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ;gBACF;;QACF;wCAAG;QAAC;KAAW;IAEf,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,8DAA8D;YAC7D,OAAe,mBAAmB,GAAG;gBACpC,OAAO;gBACP;gBACA;gBACA,SAAS;qDAAE,IAAM;;YACnB;QACF;wCAAG;QAAC;QAAW;QAAS;QAAY;KAAiB;IAErD,MAAM,2BAA2B;QAC/B,IAAI,eAAe,iBAAiB,aAAa,EAAE,OAAO;QAC1D,IAAI,cAAc,OAAO;QACzB,IAAI,OAAO,OAAO;QAClB,OAAO;IACT;IAEA,MAAM,0BAA0B;QAC9B,IAAI,eAAe,iBAAiB,aAAa,EAAE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAC1E,IAAI,cAAc,qBAAO,6LAAC,oNAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC5C,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;kBACrC,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAW,CAAC,4EAA4E,EAAE,4BAA4B;;4BACxH;0CACD,6LAAC;gCAAK,WAAU;0CACb,eAAe,iBAAiB,aAAa,GAAG,SAChD,eAAe,kBACf,QAAQ,UAAU;;;;;;;;;;;;;;;;;8BAMzB,6LAAC,4LAAA,CAAA,kBAAe;8BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,MAAM;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAC/B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;;;;;8BAO9C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,SAAS;4BAAE;4BACpB,QAAQ;4BACR,IAAI;4BACJ,KAAK;4BACL,WAAW;;;;;;sCAIb,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,SAAS;4BAAE;4BACpB,QAAQ;4BACR,WAAW;;;;;;wBAIZ,CAAC,eAAe,CAAC,8BAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CACV,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG;;;;;;gCAE9B,CAAC,6BACA,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAQN,8BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD;GAzUM;KAAA;uCA2US", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateWithAgent.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Bot, Loader2 } from \"lucide-react\";\r\n\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\nimport LiveStreamingAvatar from \"./LiveStreamingAvatar\";\r\n\r\ntype CandidateWithAgentProps = {\r\n  className?: string;\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  useAgent?: boolean;\r\n  message?: string;\r\n  onVideoReady?: () => void;\r\n  onVideoEnd?: () => void;\r\n  useStreaming?: boolean;\r\n  avatarMode?: \"standard\" | \"streaming\" | \"live\";\r\n  // Live streaming specific props\r\n  onConnectionReady?: () => void;\r\n  onStreamReady?: () => void;\r\n  onSpeechStart?: () => void;\r\n  onSpeechEnd?: () => void;\r\n  autoConnect?: boolean;\r\n  greetingMessage?: string;\r\n};\r\n\r\nconst CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({\r\n  className = \"\",\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  useAgent = true,\r\n  useStreaming = true,\r\n  avatarMode = \"live\",\r\n  onConnectionReady,\r\n  onStreamReady,\r\n  onSpeechStart,\r\n  onSpeechEnd,\r\n  autoConnect = true,\r\n  greetingMessage,\r\n}) => {\r\n  const {\r\n    agent,\r\n    isCreatingAgent,\r\n    agentError,\r\n    createAgent,\r\n    isLiveStreamingEnabled,\r\n    setIsStreamConnected,\r\n    setIsStreamReady,\r\n  } = useInterview();\r\n\r\n  const [isLiveMode, setIsLiveMode] = useState<boolean>(false);\r\n\r\n  const instructions = `You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`;\r\n  const agentName = `${jobTitle} Interviewer`;\r\n\r\n  // Determine if we should use live streaming mode\r\n  useEffect(() => {\r\n    const shouldUseLiveMode = isLiveStreamingEnabled && (useStreaming || avatarMode === \"live\");\r\n    setIsLiveMode(shouldUseLiveMode);\r\n\r\n    // If not using live mode, create traditional agent\r\n    if (!shouldUseLiveMode && useAgent && !agent && !isCreatingAgent) {\r\n      createAgent(instructions, agentName);\r\n    }\r\n  }, [useAgent, agent, isCreatingAgent, createAgent, instructions, agentName, isLiveStreamingEnabled, useStreaming, avatarMode]);\r\n\r\n  // Handle live streaming events\r\n  const handleConnectionReady = () => {\r\n    setIsStreamConnected(true);\r\n    onConnectionReady?.();\r\n  };\r\n\r\n  const handleStreamReady = () => {\r\n    setIsStreamReady(true);\r\n    onStreamReady?.();\r\n  };\r\n\r\n  const handleSpeechStart = () => {\r\n    onSpeechStart?.();\r\n  };\r\n\r\n  const handleSpeechEnd = () => {\r\n    onSpeechEnd?.();\r\n  };\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {isLiveMode ? (\r\n        // Live Streaming Avatar Mode\r\n        <LiveStreamingAvatar\r\n          className=\"w-full h-full\"\r\n          candidateName={candidateName}\r\n          jobTitle={jobTitle}\r\n          onConnectionReady={handleConnectionReady}\r\n          onStreamReady={handleStreamReady}\r\n          onSpeechStart={handleSpeechStart}\r\n          onSpeechEnd={handleSpeechEnd}\r\n          autoConnect={autoConnect}\r\n          greetingMessage={greetingMessage}\r\n        />\r\n      ) : (\r\n        // Traditional Static Agent Mode\r\n        <div className=\"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden\">\r\n          {agent ? (\r\n            <div className=\"text-center w-full h-full flex flex-col\">\r\n              {/* Avatar Image */}\r\n              <div className=\"flex-1 flex items-center justify-center p-4\">\r\n                {agent.presenter?.thumbnail ? (\r\n                  <Image\r\n                    src={agent.presenter.thumbnail}\r\n                    alt={agent.preview_name}\r\n                    width={320}\r\n                    height={550}\r\n                    className=\"w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80\"\r\n                    onError={(e) => {\r\n                      console.error(\"Failed to load avatar image:\", agent.presenter.thumbnail);\r\n                      e.currentTarget.style.display = 'none';\r\n                      e.currentTarget.nextElementSibling?.classList.remove('hidden');\r\n                    }}\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center\">\r\n                    <Bot className=\"w-16 h-16 text-gray-600\" />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Fallback icon (hidden by default, shown if image fails) */}\r\n                <div className=\"hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center\">\r\n                  <Bot className=\"w-16 h-16 text-gray-600\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : isCreatingAgent ? (\r\n            <div className=\"text-center\">\r\n              <Loader2 className=\"w-12 h-12 animate-spin text-blue-500 mx-auto mb-4\" />\r\n              <p className=\"text-sm text-gray-600\">\r\n                {isLiveMode ? \"Connecting to live avatar...\" : \"Creating AI Agent...\"}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-2\">This may take a moment</p>\r\n            </div>\r\n          ) : agentError ? (\r\n            <div className=\"text-center p-4\">\r\n              <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                <Bot className=\"w-8 h-8 text-red-500\" />\r\n              </div>\r\n              <p className=\"text-sm text-red-600 mb-2\">Failed to create agent</p>\r\n              <p className=\"text-xs text-gray-500\">{agentError}</p>\r\n              <button\r\n                onClick={() => createAgent(instructions, agentName)}\r\n                className=\"mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors\"\r\n              >\r\n                Retry\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center\">\r\n              <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                <Bot className=\"w-8 h-8 text-gray-400\" />\r\n              </div>\r\n              <p className=\"text-sm text-gray-600\">\r\n                {isLiveMode ? \"Live avatar not available\" : \"No agent available\"}\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateWithAgent;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAEA;AACA;;;AANA;;;;;;AA2BA,MAAM,qBAAwD,CAAC,EAC7D,YAAY,EAAE,EACd,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,WAAW,IAAI,EACf,eAAe,IAAI,EACnB,aAAa,MAAM,EACnB,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,WAAW,EACX,cAAc,IAAI,EAClB,eAAe,EAChB;;IACC,MAAM,EACJ,KAAK,EACL,eAAe,EACf,UAAU,EACV,WAAW,EACX,sBAAsB,EACtB,oBAAoB,EACpB,gBAAgB,EACjB,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEtD,MAAM,eAAe,CAAC,kEAAkE,EAAE,SAAS,eAAe,EAAE,cAAc,kGAAkG,CAAC;IACrO,MAAM,YAAY,GAAG,SAAS,YAAY,CAAC;IAE3C,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,oBAAoB,0BAA0B,CAAC,gBAAgB,eAAe,MAAM;YAC1F,cAAc;YAEd,mDAAmD;YACnD,IAAI,CAAC,qBAAqB,YAAY,CAAC,SAAS,CAAC,iBAAiB;gBAChE,YAAY,cAAc;YAC5B;QACF;uCAAG;QAAC;QAAU;QAAO;QAAiB;QAAa;QAAc;QAAW;QAAwB;QAAc;KAAW;IAE7H,+BAA+B;IAC/B,MAAM,wBAAwB;QAC5B,qBAAqB;QACrB;IACF;IAEA,MAAM,oBAAoB;QACxB,iBAAiB;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB;IACF;IAEA,MAAM,kBAAkB;QACtB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;kBACpC,aACC,6BAA6B;sBAC7B,6LAAC,qIAAA,CAAA,UAAmB;YAClB,WAAU;YACV,eAAe;YACf,UAAU;YACV,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;;;;;mBAGnB,gCAAgC;sBAChC,6LAAC;YAAI,WAAU;sBACZ,sBACC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,SAAS,EAAE,0BAChB,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,MAAM,SAAS,CAAC,SAAS;4BAC9B,KAAK,MAAM,YAAY;4BACvB,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,SAAS,CAAC;gCACR,QAAQ,KAAK,CAAC,gCAAgC,MAAM,SAAS,CAAC,SAAS;gCACvE,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gCAChC,EAAE,aAAa,CAAC,kBAAkB,EAAE,UAAU,OAAO;4BACvD;;;;;iDAGF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;uBAInB,gCACF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCACV,aAAa,iCAAiC;;;;;;kCAEjD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;uBAE1C,2BACF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;kCACtC,6LAAC;wBACC,SAAS,IAAM,YAAY,cAAc;wBACzC,WAAU;kCACX;;;;;;;;;;;qCAKH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAE,WAAU;kCACV,aAAa,8BAA8B;;;;;;;;;;;;;;;;;;;;;;AAQ5D;GA9IM;;QAsBA,+HAAA,CAAA,eAAY;;;KAtBZ;uCAgJS", "debugId": null}}, {"offset": {"line": 2219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ntype QuestionsPageProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList className=\"h-[550px]\" />\r\n          <CandidateWithAgent\r\n            className=\"h-[550px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"lg\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Start Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsPage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,oIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA9BM;uCAgCS", "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,6LAAC;0BAAE;;;;;;0BACH,6LAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;KAhBM;uCAiBS", "debugId": null}}, {"offset": {"line": 2407, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { Arrow<PERSON><PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\n\r\ntype FinishInterviewProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\" h-[490px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n            message=\"Thank you for completing the interview. Do you have any final questions?\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Finish Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FinishInterview;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0CACd,6LAAC,oIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;gCACT,SAAQ;;;;;;0CAEV,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA/BM;uCAiCS", "debugId": null}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 2675, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,6LAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2KAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 2810, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,6LAAC,sIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;4BAA8F;0CACrF,6LAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 3039, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\"h-[490px]\"\r\n            useAgent={false} \r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0BACd,6LAAC,iIAAA,CAAA,UAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+HAAA,CAAA,UAAa;;;;;sCACd,6LAAC,oIAAA,CAAA,UAAkB;4BACjB,WAAU;4BACV,UAAU;4BACV,eAAc;4BACd,UAAS;;;;;;sCAEX,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,6LAAC,uIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 3126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport CandidateWithAgent from \"../CandidateWithAgent\";\n\ntype InterviewRecordingProps = {\n  onNext?: () => void;\n};\n\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateWithAgent\n            className=\"h-[550px]\"\n            useAgent={true}\n            candidateName=\"Jonathan\"\n            jobTitle=\"Insurance Agent\"\n          />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            // disabled\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          02:00\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewRecording;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,oIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;KAjCM;uCAmCS", "debugId": null}}, {"offset": {"line": 3241, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport InterviewRecording from \"../../../components/interview/InterviewRecording\";\r\nimport { InterviewProvider } from \"@/context/InterviewContext\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"recording\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <InterviewProvider>\r\n      <div>{renderCurrentComponent()}</div>\r\n    </InterviewProvider>\r\n  );\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAgBA,MAAM,YAAY;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,6LAAC,iJAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,uIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,oBAAiB;kBAChB,cAAA,6LAAC;sBAAK;;;;;;;;;;;AAGZ;GAjCM;KAAA;uCAmCS", "debugId": null}}]}