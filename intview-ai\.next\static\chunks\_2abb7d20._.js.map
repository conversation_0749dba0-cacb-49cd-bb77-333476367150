{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\n\r\ntype InterviewInstructionsProps = {\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  languages?: string[];\r\n  instructions?: string[];\r\n  environmentChecklist?: string[];\r\n  disclaimers?: string[];\r\n  onNext?: () => void;\r\n};\r\n\r\nconst defaultInstructions = [\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n];\r\n\r\nconst defaultEnvironment = [\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n];\r\n\r\nconst defaultDisclaimers = [\r\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\r\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\r\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\r\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\r\n];\r\n\r\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  languages = [\"English\", \"Chinese\"],\r\n  instructions = defaultInstructions,\r\n  environmentChecklist = defaultEnvironment,\r\n  disclaimers = defaultDisclaimers,\r\n  onNext,\r\n}) => {\r\n  const [isChecked, setIsChecked] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\r\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\r\n        <p className=\"font-semibold mb-8 text-xl\">\r\n          Instructions for Interview!\r\n        </p>\r\n        <div className=\"space-y-6\">\r\n          <div>\r\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\r\n            <p className=\"text-sm mb-4\">\r\n              As part of the process you are required to complete an AI video\r\n              assessment for the role of the {jobTitle}.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {languages.map((language, index) => (\r\n                <li key={index}>{language}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {instructions.map((instruction, index) => (\r\n                <li key={index}>{instruction}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {environmentChecklist.map((item, index) => (\r\n                <li key={index}>{item}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index}>{disclaimer}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"flex items-start gap-2 mt-6\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              checked={isChecked}\r\n              onChange={(e) => setIsChecked(e.target.checked)}\r\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\r\n              By checking this box, you agree with AI Interview{\" \"}\r\n              <span className=\"text-primary cursor-pointer font-medium\">\r\n                Terms of use\r\n              </span>\r\n              .\r\n            </label>\r\n          </div>\r\n          <div className=\"flex justify-center\">\r\n            <Button\r\n              disabled={!isChecked}\r\n              variant=\"default\"\r\n              size=\"lg\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext && onNext()}\r\n            >\r\n            Proceed\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewInstructions;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,6LAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,6LAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA9FM;KAAA;uCAgGS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAoC;sDAClC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/services/didLiveStreamingService.ts"], "sourcesContent": ["\"use client\";\n\nconst DID_API_URL = \"https://api.d-id.com\";\n\nexport interface Agent {\n  id: string;\n  preview_name: string;\n  status: string;\n  presenter: {\n    type: string;\n    voice: {\n      type: string;\n      voice_id: string;\n    };\n    thumbnail: string;\n    source_url: string;\n  };\n  llm: {\n    type: string;\n    provider: string;\n    model: string;\n    instructions: string;\n  };\n}\n\nexport interface StreamSession {\n  id: string;\n  offer: RTCSessionDescriptionInit;\n  ice_servers: RTCIceServer[];\n  session_id: string;\n}\n\nexport interface ChatSession {\n  id: string;\n  agent_id: string;\n}\n\nexport class DIDLiveStreamingService {\n  private apiKey: string;\n  private peerConnection: RTCPeerConnection | null = null;\n  private streamId: string | null = null;\n  private sessionId: string | null = null;\n  private agentId: string | null = null;\n  private chatId: string | null = null;\n  private statsIntervalId: NodeJS.Timeout | null = null;\n  private videoIsPlaying: boolean = false;\n  private lastBytesReceived: number = 0;\n\n  // Event callbacks\n  public onVideoStatusChange?: (isPlaying: boolean, stream?: MediaStream) => void;\n  public onConnectionStateChange?: (state: RTCPeerConnectionState) => void;\n  public onIceConnectionStateChange?: (state: RTCIceConnectionState) => void;\n  public onAgentMessage?: (message: string) => void;\n  public onError?: (error: string) => void;\n\n  constructor(apiKey: string) {\n    this.apiKey = apiKey;\n  }\n\n  private getAuthHeaders() {\n    return {\n      \"Authorization\": `Basic ${this.apiKey}`,\n      \"Content-Type\": \"application/json\",\n    };\n  }\n\n  private async fetchWithRetries(url: string, options: RequestInit, retries = 1): Promise<Response> {\n    const maxRetryCount = 3;\n    const maxDelaySec = 4;\n    \n    try {\n      return await fetch(url, options);\n    } catch (err) {\n      if (retries <= maxRetryCount) {\n        const delay = Math.min(Math.pow(2, retries) / 4 + Math.random(), maxDelaySec) * 1000;\n        await new Promise((resolve) => setTimeout(resolve, delay));\n        console.log(`Request failed, retrying ${retries}/${maxRetryCount}. Error ${err}`);\n        return this.fetchWithRetries(url, options, retries + 1);\n      } else {\n        throw new Error(`Max retries exceeded. error: ${err}`);\n      }\n    }\n  }\n\n  async createAgent(instructions: string, agentName: string): Promise<Agent> {\n    const payload = {\n      presenter: {\n        type: \"talk\",\n        voice: {\n          type: \"microsoft\",\n          voice_id: \"en-US-JennyMultilingualV2Neural\"\n        },\n        thumbnail: \"https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg\",\n        source_url: \"https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg\"\n      },\n      llm: {\n        type: \"openai\",\n        provider: \"openai\",\n        model: \"gpt-4o-mini\",\n        instructions: instructions\n      },\n      preview_name: agentName\n    };\n\n    try {\n      const response = await this.fetchWithRetries(`${DID_API_URL}/agents`, {\n        method: \"POST\",\n        headers: this.getAuthHeaders(),\n        body: JSON.stringify(payload),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const agentData: Agent = await response.json();\n      this.agentId = agentData.id;\n      return agentData;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : \"Failed to create agent\";\n      this.onError?.(`Agent Creation Failed: ${errorMessage}`);\n      throw error;\n    }\n  }\n\n  async createChatSession(agentId: string): Promise<ChatSession> {\n    try {\n      const response = await this.fetchWithRetries(`${DID_API_URL}/agents/${agentId}/chat`, {\n        method: \"POST\",\n        headers: this.getAuthHeaders(),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to create chat session: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const chatData: ChatSession = await response.json();\n      this.chatId = chatData.id;\n      return chatData;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : \"Failed to create chat session\";\n      this.onError?.(`Chat Session Creation Failed: ${errorMessage}`);\n      throw error;\n    }\n  }\n\n  async createStreamSession(): Promise<StreamSession> {\n    try {\n      const response = await this.fetchWithRetries(`${DID_API_URL}/clips/streams`, {\n        method: 'POST',\n        headers: this.getAuthHeaders(),\n        body: JSON.stringify({\n          source_url: 'https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg',\n        }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to create stream session: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const streamData = await response.json();\n      this.streamId = streamData.id;\n      this.sessionId = streamData.session_id;\n      \n      return {\n        id: streamData.id,\n        offer: streamData.offer,\n        ice_servers: streamData.ice_servers,\n        session_id: streamData.session_id\n      };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : \"Failed to create stream session\";\n      this.onError?.(`Stream Session Creation Failed: ${errorMessage}`);\n      throw error;\n    }\n  }\n\n  private onIceCandidate = (event: RTCPeerConnectionIceEvent) => {\n    if (event.candidate && this.streamId && this.sessionId) {\n      const { candidate, sdpMid, sdpMLineIndex } = event.candidate;\n\n      fetch(`${DID_API_URL}/clips/streams/${this.streamId}/ice`, {\n        method: 'POST',\n        headers: this.getAuthHeaders(),\n        body: JSON.stringify({\n          candidate,\n          sdpMid,\n          sdpMLineIndex,\n          session_id: this.sessionId,\n        }),\n      }).catch(error => {\n        console.error('Failed to send ICE candidate:', error);\n      });\n    }\n  };\n\n  private onTrack = (event: RTCTrackEvent) => {\n    if (!event.track) return;\n\n    this.statsIntervalId = setInterval(async () => {\n      if (!this.peerConnection) return;\n      \n      const stats = await this.peerConnection.getStats(event.track);\n      stats.forEach((report) => {\n        if (report.type === 'inbound-rtp' && report.kind === 'video') {\n          const videoStatusChanged = this.videoIsPlaying !== report.bytesReceived > this.lastBytesReceived;\n\n          if (videoStatusChanged) {\n            this.videoIsPlaying = report.bytesReceived > this.lastBytesReceived;\n            this.onVideoStatusChange?.(this.videoIsPlaying, event.streams[0]);\n          }\n          this.lastBytesReceived = report.bytesReceived;\n        }\n      });\n    }, 500);\n  };\n\n  async createPeerConnection(offer: RTCSessionDescriptionInit, iceServers: RTCIceServer[]): Promise<RTCSessionDescriptionInit> {\n    if (this.peerConnection) {\n      this.closePeerConnection();\n    }\n\n    this.peerConnection = new RTCPeerConnection({ iceServers });\n    \n    // Set up event listeners\n    this.peerConnection.addEventListener('icecandidate', this.onIceCandidate);\n    this.peerConnection.addEventListener('track', this.onTrack);\n    this.peerConnection.addEventListener('connectionstatechange', () => {\n      if (this.peerConnection) {\n        this.onConnectionStateChange?.(this.peerConnection.connectionState);\n        if (this.peerConnection.connectionState === 'failed' || this.peerConnection.connectionState === 'closed') {\n          this.closePeerConnection();\n        }\n      }\n    });\n    this.peerConnection.addEventListener('iceconnectionstatechange', () => {\n      if (this.peerConnection) {\n        this.onIceConnectionStateChange?.(this.peerConnection.iceConnectionState);\n        if (this.peerConnection.iceConnectionState === 'failed' || this.peerConnection.iceConnectionState === 'closed') {\n          this.closePeerConnection();\n        }\n      }\n    });\n\n    await this.peerConnection.setRemoteDescription(offer);\n    const sessionClientAnswer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(sessionClientAnswer);\n\n    // Create data channel for agent responses\n    const dataChannel = this.peerConnection.createDataChannel('JanusDataChannel');\n    dataChannel.onopen = () => {\n      console.log('Data channel opened');\n    };\n\n    let decodedMsg: string;\n    dataChannel.onmessage = (event) => {\n      let msg = event.data;\n      const msgType = 'chat/answer:';\n      if (msg.includes(msgType)) {\n        msg = decodeURIComponent(msg.replace(msgType, ''));\n        decodedMsg = msg;\n        return decodedMsg;\n      }\n      if (msg.includes('stream/started')) {\n        this.onAgentMessage?.(decodedMsg);\n      }\n    };\n\n    dataChannel.onclose = () => {\n      console.log('Data channel closed');\n    };\n\n    return sessionClientAnswer;\n  }\n\n  async startStream(sessionClientAnswer: RTCSessionDescriptionInit): Promise<void> {\n    if (!this.streamId || !this.sessionId) {\n      throw new Error('Stream session not created');\n    }\n\n    try {\n      const response = await fetch(`${DID_API_URL}/clips/streams/${this.streamId}/sdp`, {\n        method: 'POST',\n        headers: this.getAuthHeaders(),\n        body: JSON.stringify({\n          answer: sessionClientAnswer,\n          session_id: this.sessionId,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to start stream: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : \"Failed to start stream\";\n      this.onError?.(`Stream Start Failed: ${errorMessage}`);\n      throw error;\n    }\n  }\n\n  async sendMessage(message: string): Promise<void> {\n    if (!this.agentId || !this.chatId || !this.streamId || !this.sessionId) {\n      throw new Error('Agent, chat session, or stream session not initialized');\n    }\n\n    try {\n      const response = await this.fetchWithRetries(`${DID_API_URL}/agents/${this.agentId}/chat/${this.chatId}`, {\n        method: 'POST',\n        headers: this.getAuthHeaders(),\n        body: JSON.stringify({\n          streamId: this.streamId,\n          sessionId: this.sessionId,\n          messages: [\n            {\n              role: 'user',\n              content: message,\n              created_at: new Date().toString(),\n            },\n          ],\n        }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to send message: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const responseData = await response.json();\n      if (response.status === 200 && responseData.chatMode === 'TextOnly') {\n        console.log('User is out of credit, API only return text messages');\n        this.onAgentMessage?.(responseData.result);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : \"Failed to send message\";\n      this.onError?.(`Message Send Failed: ${errorMessage}`);\n      throw error;\n    }\n  }\n\n  async destroyStream(): Promise<void> {\n    if (this.streamId && this.sessionId) {\n      try {\n        await fetch(`${DID_API_URL}/clips/streams/${this.streamId}`, {\n          method: 'DELETE',\n          headers: this.getAuthHeaders(),\n          body: JSON.stringify({ session_id: this.sessionId }),\n        });\n      } catch (error) {\n        console.error('Failed to destroy stream:', error);\n      }\n    }\n\n    this.closePeerConnection();\n    this.streamId = null;\n    this.sessionId = null;\n  }\n\n  private closePeerConnection(): void {\n    if (this.statsIntervalId) {\n      clearInterval(this.statsIntervalId);\n      this.statsIntervalId = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.removeEventListener('icecandidate', this.onIceCandidate);\n      this.peerConnection.removeEventListener('track', this.onTrack);\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.videoIsPlaying = false;\n    this.lastBytesReceived = 0;\n  }\n\n  // Utility methods\n  isConnected(): boolean {\n    return this.peerConnection?.connectionState === 'connected';\n  }\n\n  isStreamReady(): boolean {\n    return this.streamId !== null && this.sessionId !== null;\n  }\n\n  isAgentReady(): boolean {\n    return this.agentId !== null && this.chatId !== null;\n  }\n\n  getAgentId(): string | null {\n    return this.agentId;\n  }\n\n  getChatId(): string | null {\n    return this.chatId;\n  }\n\n  getStreamId(): string | null {\n    return this.streamId;\n  }\n\n  // Clean up resources\n  destroy(): void {\n    this.destroyStream();\n    this.agentId = null;\n    this.chatId = null;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA,MAAM,cAAc;AAmCb,MAAM;IACH,OAAe;IACf,iBAA2C,KAAK;IAChD,WAA0B,KAAK;IAC/B,YAA2B,KAAK;IAChC,UAAyB,KAAK;IAC9B,SAAwB,KAAK;IAC7B,kBAAyC,KAAK;IAC9C,iBAA0B,MAAM;IAChC,oBAA4B,EAAE;IAEtC,kBAAkB;IACX,oBAAyE;IACzE,wBAAkE;IAClE,2BAAoE;IACpE,eAA2C;IAC3C,QAAkC;IAEzC,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEQ,iBAAiB;QACvB,OAAO;YACL,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACvC,gBAAgB;QAClB;IACF;IAEA,MAAc,iBAAiB,GAAW,EAAE,OAAoB,EAAE,UAAU,CAAC,EAAqB;QAChG,MAAM,gBAAgB;QACtB,MAAM,cAAc;QAEpB,IAAI;YACF,OAAO,MAAM,MAAM,KAAK;QAC1B,EAAE,OAAO,KAAK;YACZ,IAAI,WAAW,eAAe;gBAC5B,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,KAAK,MAAM,IAAI,eAAe;gBAChF,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBACnD,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,EAAE,cAAc,QAAQ,EAAE,KAAK;gBAChF,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,SAAS,UAAU;YACvD,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK;YACvD;QACF;IACF;IAEA,MAAM,YAAY,YAAoB,EAAE,SAAiB,EAAkB;QACzE,MAAM,UAAU;YACd,WAAW;gBACT,MAAM;gBACN,OAAO;oBACL,MAAM;oBACN,UAAU;gBACZ;gBACA,WAAW;gBACX,YAAY;YACd;YACA,KAAK;gBACH,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,cAAc;YAChB;YACA,cAAc;QAChB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,YAAY,OAAO,CAAC,EAAE;gBACpE,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;gBAC5B,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;YAEA,MAAM,YAAmB,MAAM,SAAS,IAAI;YAC5C,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;YAC3B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,IAAI,CAAC,OAAO,GAAG,CAAC,uBAAuB,EAAE,cAAc;YACvD,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,OAAe,EAAwB;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,YAAY,QAAQ,EAAE,QAAQ,KAAK,CAAC,EAAE;gBACpF,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;YAC9B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YAC3G;YAEA,MAAM,WAAwB,MAAM,SAAS,IAAI;YACjD,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE;YACzB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,IAAI,CAAC,OAAO,GAAG,CAAC,8BAA8B,EAAE,cAAc;YAC9D,MAAM;QACR;IACF;IAEA,MAAM,sBAA8C;QAClD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,YAAY,cAAc,CAAC,EAAE;gBAC3E,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;gBAC5B,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY;gBACd;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YAC7G;YAEA,MAAM,aAAa,MAAM,SAAS,IAAI;YACtC,IAAI,CAAC,QAAQ,GAAG,WAAW,EAAE;YAC7B,IAAI,CAAC,SAAS,GAAG,WAAW,UAAU;YAEtC,OAAO;gBACL,IAAI,WAAW,EAAE;gBACjB,OAAO,WAAW,KAAK;gBACvB,aAAa,WAAW,WAAW;gBACnC,YAAY,WAAW,UAAU;YACnC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,IAAI,CAAC,OAAO,GAAG,CAAC,gCAAgC,EAAE,cAAc;YAChE,MAAM;QACR;IACF;IAEQ,iBAAiB,CAAC;QACxB,IAAI,MAAM,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACtD,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,SAAS;YAE5D,MAAM,GAAG,YAAY,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;gBAC5B,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA,YAAY,IAAI,CAAC,SAAS;gBAC5B;YACF,GAAG,KAAK,CAAC,CAAA;gBACP,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;IACF,EAAE;IAEM,UAAU,CAAC;QACjB,IAAI,CAAC,MAAM,KAAK,EAAE;QAElB,IAAI,CAAC,eAAe,GAAG,YAAY;YACjC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAE1B,MAAM,QAAQ,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,KAAK;YAC5D,MAAM,OAAO,CAAC,CAAC;gBACb,IAAI,OAAO,IAAI,KAAK,iBAAiB,OAAO,IAAI,KAAK,SAAS;oBAC5D,MAAM,qBAAqB,IAAI,CAAC,cAAc,KAAK,OAAO,aAAa,GAAG,IAAI,CAAC,iBAAiB;oBAEhG,IAAI,oBAAoB;wBACtB,IAAI,CAAC,cAAc,GAAG,OAAO,aAAa,GAAG,IAAI,CAAC,iBAAiB;wBACnE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,OAAO,CAAC,EAAE;oBAClE;oBACA,IAAI,CAAC,iBAAiB,GAAG,OAAO,aAAa;gBAC/C;YACF;QACF,GAAG;IACL,EAAE;IAEF,MAAM,qBAAqB,KAAgC,EAAE,UAA0B,EAAsC;QAC3H,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,cAAc,GAAG,IAAI,kBAAkB;YAAE;QAAW;QAEzD,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,CAAC,cAAc;QACxE,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,IAAI,CAAC,OAAO;QAC1D,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,yBAAyB;YAC5D,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe;gBAClE,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,KAAK,YAAY,IAAI,CAAC,cAAc,CAAC,eAAe,KAAK,UAAU;oBACxG,IAAI,CAAC,mBAAmB;gBAC1B;YACF;QACF;QACA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,4BAA4B;YAC/D,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB;gBACxE,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB,KAAK,YAAY,IAAI,CAAC,cAAc,CAAC,kBAAkB,KAAK,UAAU;oBAC9G,IAAI,CAAC,mBAAmB;gBAC1B;YACF;QACF;QAEA,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC;QAC/C,MAAM,sBAAsB,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC;QAE9C,0CAA0C;QAC1C,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;QAC1D,YAAY,MAAM,GAAG;YACnB,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI;QACJ,YAAY,SAAS,GAAG,CAAC;YACvB,IAAI,MAAM,MAAM,IAAI;YACpB,MAAM,UAAU;YAChB,IAAI,IAAI,QAAQ,CAAC,UAAU;gBACzB,MAAM,mBAAmB,IAAI,OAAO,CAAC,SAAS;gBAC9C,aAAa;gBACb,OAAO;YACT;YACA,IAAI,IAAI,QAAQ,CAAC,mBAAmB;gBAClC,IAAI,CAAC,cAAc,GAAG;YACxB;QACF;QAEA,YAAY,OAAO,GAAG;YACpB,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT;IAEA,MAAM,YAAY,mBAA8C,EAAiB;QAC/E,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAChF,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;gBAC5B,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,YAAY,IAAI,CAAC,SAAS;gBAC5B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,IAAI,CAAC,OAAO,GAAG,CAAC,qBAAqB,EAAE,cAAc;YACrD,MAAM;QACR;IACF;IAEA,MAAM,YAAY,OAAe,EAAiB;QAChD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACtE,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,YAAY,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE;gBACxG,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;gBAC5B,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,IAAI,CAAC,QAAQ;oBACvB,WAAW,IAAI,CAAC,SAAS;oBACzB,UAAU;wBACR;4BACE,MAAM;4BACN,SAAS;4BACT,YAAY,IAAI,OAAO,QAAQ;wBACjC;qBACD;gBACH;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;YAEA,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,IAAI,SAAS,MAAM,KAAK,OAAO,aAAa,QAAQ,KAAK,YAAY;gBACnE,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,cAAc,GAAG,aAAa,MAAM;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,IAAI,CAAC,OAAO,GAAG,CAAC,qBAAqB,EAAE,cAAc;YACrD,MAAM;QACR;IACF;IAEA,MAAM,gBAA+B;QACnC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI;gBACF,MAAM,MAAM,GAAG,YAAY,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAC3D,QAAQ;oBACR,SAAS,IAAI,CAAC,cAAc;oBAC5B,MAAM,KAAK,SAAS,CAAC;wBAAE,YAAY,IAAI,CAAC,SAAS;oBAAC;gBACpD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;IACnB;IAEQ,sBAA4B;QAClC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,cAAc,IAAI,CAAC,eAAe;YAClC,IAAI,CAAC,eAAe,GAAG;QACzB;QAEA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,gBAAgB,IAAI,CAAC,cAAc;YAC3E,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC,OAAO;YAC7D,IAAI,CAAC,cAAc,CAAC,KAAK;YACzB,IAAI,CAAC,cAAc,GAAG;QACxB;QAEA,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA,kBAAkB;IAClB,cAAuB;QACrB,OAAO,IAAI,CAAC,cAAc,EAAE,oBAAoB;IAClD;IAEA,gBAAyB;QACvB,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,SAAS,KAAK;IACtD;IAEA,eAAwB;QACtB,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK;IAClD;IAEA,aAA4B;QAC1B,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,YAA2B;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,cAA6B;QAC3B,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,qBAAqB;IACrB,UAAgB;QACd,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/InterviewContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, useCallback, ReactNode, useRef, useEffect } from \"react\";\r\nimport { DIDLiveStreamingService, Agent } from \"@/services/didLiveStreamingService\";\r\n\r\ninterface InterviewContextType {\r\n  // D-ID Agent state\r\n  agent: Agent | null;\r\n  isCreatingAgent: boolean;\r\n  agentError: string | null;\r\n  createAgent: (instructions: string, agentName: string) => Promise<void>;\r\n\r\n  // Live streaming state\r\n  isStreamConnected: boolean;\r\n  isStreamConnecting: boolean;\r\n  streamError: string | null;\r\n  videoStream: MediaStream | null;\r\n  connectStream: () => Promise<void>;\r\n  disconnectStream: () => Promise<void>;\r\n  sendMessage: (message: string) => Promise<void>;\r\n\r\n  // Chat state\r\n  chatMessages: Array<{ role: 'user' | 'agent'; content: string; timestamp: Date }>;\r\n\r\n  // Interview state\r\n  currentQuestion: number;\r\n  setCurrentQuestion: (question: number) => void;\r\n  isInterviewStarted: boolean;\r\n  setIsInterviewStarted: (started: boolean) => void;\r\n  speakQuestion: (questionIndex: number) => Promise<void>;\r\n\r\n  // Questions data\r\n  questions: string[];\r\n}\r\n\r\nconst InterviewContext = createContext<InterviewContextType | undefined>(undefined);\r\n\r\ninterface InterviewProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {\r\n  // D-ID Agent state\r\n  const [agent, setAgent] = useState<Agent | null>(null);\r\n  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);\r\n  const [agentError, setAgentError] = useState<string | null>(null);\r\n\r\n  // Live streaming state\r\n  const [isStreamConnected, setIsStreamConnected] = useState<boolean>(false);\r\n  const [isStreamConnecting, setIsStreamConnecting] = useState<boolean>(false);\r\n  const [streamError, setStreamError] = useState<string | null>(null);\r\n  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);\r\n\r\n  // Chat state\r\n  const [chatMessages, setChatMessages] = useState<Array<{ role: 'user' | 'agent'; content: string; timestamp: Date }>>([]);\r\n\r\n  // Interview state\r\n  const [currentQuestion, setCurrentQuestion] = useState<number>(1);\r\n  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);\r\n\r\n  // Questions data\r\n  const questions = [\r\n    \"Tell us about yourself?\",\r\n    \"What are your strengths?\",\r\n    \"Why do you want this job?\",\r\n    \"Where do you see yourself in 5 years?\",\r\n  ];\r\n\r\n  // Live streaming service reference\r\n  const streamingServiceRef = useRef<DIDLiveStreamingService | null>(null);\r\n\r\n  // Initialize streaming service\r\n  useEffect(() => {\r\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\";\r\n    if (apiKey && !streamingServiceRef.current) {\r\n      streamingServiceRef.current = new DIDLiveStreamingService(apiKey);\r\n\r\n      // Set up event handlers\r\n      streamingServiceRef.current.onVideoStatusChange = (isPlaying: boolean, stream?: MediaStream) => {\r\n        if (isPlaying && stream) {\r\n          setVideoStream(stream);\r\n        } else {\r\n          setVideoStream(null);\r\n        }\r\n      };\r\n\r\n      streamingServiceRef.current.onConnectionStateChange = (state: RTCPeerConnectionState) => {\r\n        setIsStreamConnected(state === 'connected');\r\n        if (state === 'failed' || state === 'closed') {\r\n          setIsStreamConnecting(false);\r\n          setStreamError('Connection failed');\r\n        }\r\n      };\r\n\r\n      streamingServiceRef.current.onAgentMessage = (message: string) => {\r\n        setChatMessages(prev => [...prev, {\r\n          role: 'agent',\r\n          content: message,\r\n          timestamp: new Date()\r\n        }]);\r\n      };\r\n\r\n      streamingServiceRef.current.onError = (error: string) => {\r\n        setStreamError(error);\r\n        setIsStreamConnecting(false);\r\n      };\r\n    }\r\n  }, []);\r\n\r\n  const createAgent = useCallback(async (instructions: string, agentName: string) => {\r\n    // If agent already exists with same instructions, don't recreate\r\n    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {\r\n      return;\r\n    }\r\n\r\n    setIsCreatingAgent(true);\r\n    setAgentError(null);\r\n\r\n    try {\r\n      if (!streamingServiceRef.current) {\r\n        throw new Error('Streaming service not initialized');\r\n      }\r\n\r\n      const agentData = await streamingServiceRef.current.createAgent(instructions, agentName);\r\n      console.log(\"D-ID Agent Created Successfully:\", agentData);\r\n      setAgent(agentData);\r\n\r\n      // Create chat session\r\n      await streamingServiceRef.current.createChatSession(agentData.id);\r\n    } catch (err: unknown) {\r\n      console.error(\"D-ID Agent Creation Error:\", err);\r\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create agent\";\r\n      setAgentError(`Agent Creation Failed: ${errorMessage}`);\r\n    } finally {\r\n      setIsCreatingAgent(false);\r\n    }\r\n  }, [agent]);\r\n\r\n  const connectStream = useCallback(async () => {\r\n    if (!streamingServiceRef.current || !streamingServiceRef.current.isAgentReady()) {\r\n      setStreamError('Agent not ready');\r\n      return;\r\n    }\r\n\r\n    setIsStreamConnecting(true);\r\n    setStreamError(null);\r\n\r\n    try {\r\n      // Create stream session\r\n      const streamSession = await streamingServiceRef.current.createStreamSession();\r\n\r\n      // Create peer connection\r\n      const answer = await streamingServiceRef.current.createPeerConnection(\r\n        streamSession.offer,\r\n        streamSession.ice_servers\r\n      );\r\n\r\n      // Start stream\r\n      await streamingServiceRef.current.startStream(answer);\r\n\r\n      console.log('Stream connected successfully');\r\n    } catch (error) {\r\n      console.error('Failed to connect stream:', error);\r\n      const errorMessage = error instanceof Error ? error.message : \"Failed to connect stream\";\r\n      setStreamError(errorMessage);\r\n    } finally {\r\n      setIsStreamConnecting(false);\r\n    }\r\n  }, []);\r\n\r\n  const disconnectStream = useCallback(async () => {\r\n    if (streamingServiceRef.current) {\r\n      await streamingServiceRef.current.destroyStream();\r\n      setIsStreamConnected(false);\r\n      setVideoStream(null);\r\n      setStreamError(null);\r\n    }\r\n  }, []);\r\n\r\n  const sendMessage = useCallback(async (message: string) => {\r\n    if (!streamingServiceRef.current || !streamingServiceRef.current.isAgentReady()) {\r\n      throw new Error('Agent not ready');\r\n    }\r\n\r\n    // Add user message to chat\r\n    setChatMessages(prev => [...prev, {\r\n      role: 'user',\r\n      content: message,\r\n      timestamp: new Date()\r\n    }]);\r\n\r\n    await streamingServiceRef.current.sendMessage(message);\r\n  }, []);\r\n\r\n  const speakQuestion = useCallback(async (questionIndex: number) => {\r\n    if (questionIndex < 0 || questionIndex >= questions.length) {\r\n      throw new Error('Invalid question index');\r\n    }\r\n\r\n    const question = questions[questionIndex];\r\n    await sendMessage(`Please ask this interview question: \"${question}\"`);\r\n  }, [questions, sendMessage]);\r\n\r\n  const value: InterviewContextType = {\r\n    // D-ID Agent state\r\n    agent,\r\n    isCreatingAgent,\r\n    agentError,\r\n    createAgent,\r\n\r\n    // Live streaming state\r\n    isStreamConnected,\r\n    isStreamConnecting,\r\n    streamError,\r\n    videoStream,\r\n    connectStream,\r\n    disconnectStream,\r\n    sendMessage,\r\n\r\n    // Chat state\r\n    chatMessages,\r\n\r\n    // Interview state\r\n    currentQuestion,\r\n    setCurrentQuestion,\r\n    isInterviewStarted,\r\n    setIsInterviewStarted,\r\n    speakQuestion,\r\n\r\n    // Questions data\r\n    questions,\r\n  };\r\n\r\n  return (\r\n    <InterviewContext.Provider value={value}>\r\n      {children}\r\n    </InterviewContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useInterview = (): InterviewContextType => {\r\n  const context = useContext(InterviewContext);\r\n  if (context === undefined) {\r\n    throw new Error('useInterview must be used within an InterviewProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;AAwEmB;;AAvEnB;AACA;;;AAFA;;;AAkCA,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,MAAM,oBAAsD,CAAC,EAAE,QAAQ,EAAE;;IAC9E,mBAAmB;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,uBAAuB;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,aAAa;IACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuE,EAAE;IAExH,kBAAkB;IAClB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,iBAAiB;IACjB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,mCAAmC;IACnC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkC;IAEnE,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,SAAS,4FAAuC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,IAAI;YACjF,IAAI,UAAU,CAAC,oBAAoB,OAAO,EAAE;gBAC1C,oBAAoB,OAAO,GAAG,IAAI,sIAAA,CAAA,0BAAuB,CAAC;gBAE1D,wBAAwB;gBACxB,oBAAoB,OAAO,CAAC,mBAAmB;mDAAG,CAAC,WAAoB;wBACrE,IAAI,aAAa,QAAQ;4BACvB,eAAe;wBACjB,OAAO;4BACL,eAAe;wBACjB;oBACF;;gBAEA,oBAAoB,OAAO,CAAC,uBAAuB;mDAAG,CAAC;wBACrD,qBAAqB,UAAU;wBAC/B,IAAI,UAAU,YAAY,UAAU,UAAU;4BAC5C,sBAAsB;4BACtB,eAAe;wBACjB;oBACF;;gBAEA,oBAAoB,OAAO,CAAC,cAAc;mDAAG,CAAC;wBAC5C;2DAAgB,CAAA,OAAQ;uCAAI;oCAAM;wCAChC,MAAM;wCACN,SAAS;wCACT,WAAW,IAAI;oCACjB;iCAAE;;oBACJ;;gBAEA,oBAAoB,OAAO,CAAC,OAAO;mDAAG,CAAC;wBACrC,eAAe;wBACf,sBAAsB;oBACxB;;YACF;QACF;sCAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO,cAAsB;YAC3D,iEAAiE;YACjE,IAAI,SAAS,MAAM,GAAG,CAAC,YAAY,KAAK,gBAAgB,MAAM,YAAY,KAAK,WAAW;gBACxF;YACF;YAEA,mBAAmB;YACnB,cAAc;YAEd,IAAI;gBACF,IAAI,CAAC,oBAAoB,OAAO,EAAE;oBAChC,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,YAAY,MAAM,oBAAoB,OAAO,CAAC,WAAW,CAAC,cAAc;gBAC9E,QAAQ,GAAG,CAAC,oCAAoC;gBAChD,SAAS;gBAET,sBAAsB;gBACtB,MAAM,oBAAoB,OAAO,CAAC,iBAAiB,CAAC,UAAU,EAAE;YAClE,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,cAAc,CAAC,uBAAuB,EAAE,cAAc;YACxD,SAAU;gBACR,mBAAmB;YACrB;QACF;qDAAG;QAAC;KAAM;IAEV,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAChC,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,oBAAoB,OAAO,CAAC,YAAY,IAAI;gBAC/E,eAAe;gBACf;YACF;YAEA,sBAAsB;YACtB,eAAe;YAEf,IAAI;gBACF,wBAAwB;gBACxB,MAAM,gBAAgB,MAAM,oBAAoB,OAAO,CAAC,mBAAmB;gBAE3E,yBAAyB;gBACzB,MAAM,SAAS,MAAM,oBAAoB,OAAO,CAAC,oBAAoB,CACnE,cAAc,KAAK,EACnB,cAAc,WAAW;gBAG3B,eAAe;gBACf,MAAM,oBAAoB,OAAO,CAAC,WAAW,CAAC;gBAE9C,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,eAAe;YACjB,SAAU;gBACR,sBAAsB;YACxB;QACF;uDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACnC,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,MAAM,oBAAoB,OAAO,CAAC,aAAa;gBAC/C,qBAAqB;gBACrB,eAAe;gBACf,eAAe;YACjB;QACF;0DAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YACrC,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,oBAAoB,OAAO,CAAC,YAAY,IAAI;gBAC/E,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B;8DAAgB,CAAA,OAAQ;2BAAI;wBAAM;4BAChC,MAAM;4BACN,SAAS;4BACT,WAAW,IAAI;wBACjB;qBAAE;;YAEF,MAAM,oBAAoB,OAAO,CAAC,WAAW,CAAC;QAChD;qDAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YACvC,IAAI,gBAAgB,KAAK,iBAAiB,UAAU,MAAM,EAAE;gBAC1D,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,SAAS,CAAC,cAAc;YACzC,MAAM,YAAY,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC;QACvE;uDAAG;QAAC;QAAW;KAAY;IAE3B,MAAM,QAA8B;QAClC,mBAAmB;QACnB;QACA;QACA;QACA;QAEA,uBAAuB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,aAAa;QACb;QAEA,kBAAkB;QAClB;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB;IACF;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;GArMa;KAAA;AAuMN,MAAM,eAAe;;IAC1B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype QuestionsListProps = {\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const { questions, currentQuestion } = useInterview();\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAOA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACU;;IACnB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAElD,qBACE,6LAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,6LAAC;gCAAK,WAAU;;;;;;0CAElB,6LAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,6LAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;GA/CM;;QAGmC,+HAAA,CAAA,eAAY;;;KAH/C;uCAiDS", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateWithAgent.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect } from \"react\";\r\nimport LiveStreamingAgent from \"./LiveStreamingAgent\";\r\n\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype CandidateWithAgentProps = {\r\n  className?: string;\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  useAgent?: boolean;\r\n  message?: string;\r\n  onVideoReady?: () => void;\r\n  onVideoEnd?: () => void;\r\n  useStreaming?: boolean;\r\n  avatarMode?: \"standard\" | \"streaming\" | \"live\";\r\n};\r\n\r\nconst CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({\r\n  className = \"\",\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  useAgent = true,\r\n\r\n}) => {\r\n  const { agent, isCreatingAgent, agentError, createAgent } = useInterview();\r\n\r\n  const instructions = `You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`;\r\n  const agentName = `${jobTitle} Interviewer`;\r\n\r\n  useEffect(() => {\r\n    if (useAgent && !agent && !isCreatingAgent) {\r\n      createAgent(instructions, agentName);\r\n    }\r\n  }, [useAgent, agent, isCreatingAgent, createAgent, instructions, agentName]);\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      <div className=\"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden\">\r\n        {agent ? (\r\n          <div className=\"text-center w-full h-full flex flex-col\">\r\n            {/* Avatar Image */}\r\n            <div className=\"flex-1 flex items-center justify-center p-4\">\r\n              {agent.presenter?.thumbnail ? (\r\n                <Image\r\n                  src={agent.presenter.thumbnail}\r\n                  alt={agent.preview_name}\r\n                  width={320}\r\n                  height={550}\r\n                  className=\"w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80\"\r\n                  onError={(e) => {\r\n                    console.error(\"Failed to load avatar image:\", agent.presenter.thumbnail);\r\n                    e.currentTarget.style.display = 'none';\r\n                    e.currentTarget.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : (\r\n                <div className=\"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center\">\r\n                  <Bot className=\"w-16 h-16 text-gray-600\" />\r\n                </div>\r\n              )}\r\n\r\n              {/* Fallback icon (hidden by default, shown if image fails) */}\r\n              <div className=\"hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center\">\r\n                <Bot className=\"w-16 h-16 text-gray-600\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Agent Info */}\r\n            {/* <div className=\"p-4 bg-white/80 backdrop-blur-sm\">\r\n              <h3 className=\"font-semibold text-lg text-gray-800\">\r\n                {agent.preview_name}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 mt-1\">\r\n                Status: {agent.status}\r\n              </p>\r\n              {message && (\r\n                <p className=\"text-sm text-blue-600 mt-2 italic\">\r\n                  \"{message}\"\r\n                </p>\r\n              )}\r\n            </div> */}\r\n          </div>\r\n        ) : isCreatingAgent ? (\r\n          <div className=\"text-center\">\r\n            <Loader2 className=\"w-12 h-12 animate-spin text-blue-500 mx-auto mb-4\" />\r\n            <p className=\"text-sm text-gray-600\">Creating AI Agent...</p>\r\n            <p className=\"text-xs text-gray-500 mt-2\">This may take a moment</p>\r\n          </div>\r\n        ) : agentError ? (\r\n          <div className=\"text-center p-4\">\r\n            <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <Bot className=\"w-8 h-8 text-red-500\" />\r\n            </div>\r\n            <p className=\"text-sm text-red-600 mb-2\">Failed to create agent</p>\r\n            <p className=\"text-xs text-gray-500\">{agentError}</p>\r\n            <button\r\n              onClick={() => createAgent(instructions, agentName)}\r\n              className=\"mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors\"\r\n            >\r\n              Retry\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center\">\r\n            <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <Bot className=\"w-8 h-8 text-gray-400\" />\r\n            </div>\r\n            <p className=\"text-sm text-gray-600\">No agent available</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateWithAgent;\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;;;AAJA;;;AAkBA,MAAM,qBAAwD,CAAC,EAC7D,YAAY,EAAE,EACd,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,WAAW,IAAI,EAEhB;;IACC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvE,MAAM,eAAe,CAAC,kEAAkE,EAAE,SAAS,eAAe,EAAE,cAAc,kGAAkG,CAAC;IACrO,MAAM,YAAY,GAAG,SAAS,YAAY,CAAC;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,YAAY,CAAC,SAAS,CAAC,iBAAiB;gBAC1C,YAAY,cAAc;YAC5B;QACF;uCAAG;QAAC;QAAU;QAAO;QAAiB;QAAa;QAAc;KAAU;IAE3E,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;kBACrC,cAAA,6LAAC;YAAI,WAAU;sBACZ,sBACC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,SAAS,EAAE,0BAChB,6LAAC;4BACC,KAAK,MAAM,SAAS,CAAC,SAAS;4BAC9B,KAAK,MAAM,YAAY;4BACvB,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,SAAS,CAAC;gCACR,QAAQ,KAAK,CAAC,gCAAgC,MAAM,SAAS,CAAC,SAAS;gCACvE,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gCAChC,EAAE,aAAa,CAAC,kBAAkB,EAAE,UAAU,OAAO;4BACvD;;;;;iDAGF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;uBAmBnB,gCACF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAQ,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;uBAE1C,2BACF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;kCACtC,6LAAC;wBACC,SAAS,IAAM,YAAY,cAAc;wBACzC,WAAU;kCACX;;;;;;;;;;;qCAKH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAMjD;GAhGM;;QAOwD,+HAAA,CAAA,eAAY;;;KAPpE;uCAkGS", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ntype QuestionsPageProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList className=\"h-[550px]\" />\r\n          <CandidateWithAgent\r\n            className=\"h-[550px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"lg\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Start Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsPage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,oIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA9BM;uCAgCS", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,6LAAC;0BAAE;;;;;;0BACH,6LAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;KAhBM;uCAiBS", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { Arrow<PERSON><PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\n\r\ntype FinishInterviewProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\" h-[490px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n            message=\"Thank you for completing the interview. Do you have any final questions?\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Finish Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FinishInterview;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0CACd,6LAAC,oIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;gCACT,SAAQ;;;;;;0CAEV,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA/BM;uCAiCS", "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,6LAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 1970, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2KAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 2032, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,6LAAC,sIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;4BAA8F;0CACrF,6LAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\"h-[490px]\"\r\n            useAgent={false} \r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0BACd,6LAAC,iIAAA,CAAA,UAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+HAAA,CAAA,UAAa;;;;;sCACd,6LAAC,oIAAA,CAAA,UAAkB;4BACjB,WAAU;4BACV,UAAU;4BACV,eAAc;4BACd,UAAS;;;;;;sCAEX,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,6LAAC,uIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 2348, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport CandidateWithAgent from \"../CandidateWithAgent\";\n\ntype InterviewRecordingProps = {\n  onNext?: () => void;\n};\n\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateWithAgent\n            className=\"h-[550px]\"\n            useAgent={true}\n            candidateName=\"Jonathan\"\n            jobTitle=\"Insurance Agent\"\n          />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            // disabled\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          02:00\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewRecording;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,oIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;KAjCM;uCAmCS", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport InterviewRecording from \"../../../components/interview/InterviewRecording\";\r\nimport { InterviewProvider } from \"@/context/InterviewContext\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"recording\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <InterviewProvider>\r\n      <div>{renderCurrentComponent()}</div>\r\n    </InterviewProvider>\r\n  );\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAgBA,MAAM,YAAY;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,6LAAC,iJAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,uIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,oBAAiB;kBAChB,cAAA,6LAAC;sBAAK;;;;;;;;;;;AAGZ;GAjCM;KAAA;uCAmCS", "debugId": null}}]}